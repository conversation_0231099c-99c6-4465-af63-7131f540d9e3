-- CreateTable
CREATE TABLE `analytics` (
    `interaction_id` INTEGER NOT NULL AUTO_INCREMENT,
    `employer_id` INTEGER NULL,
    `profile_id` INTEGER NULL,
    `interaction_type` ENUM('view', 'click') NULL,
    `timestamp` DATETIME(0) NULL DEFAULT CURRENT_TIMESTAMP(0),

    INDEX `fk_employer`(`employer_id`),
    INDEX `fk_profile`(`profile_id`),
    PRIMARY KEY (`interaction_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `company` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `company_name` VARCHAR(255) NOT NULL,
    `company_description` TEXT NULL,
    `company_website` VARCHAR(255) NULL,
    `company_logo` VARCHAR(255) NULL,
    `gst` VARCHAR(20) NULL,
    `superuser` INTEGER NULL,
    `created_at` TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    `updated_at` TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),

    INDEX `idx_company_name`(`company_name`),
    INDEX `idx_company_website`(`company_website`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `employer` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `emp_id` INTEGER NOT NULL,
    `emp_name` VARCHAR(50) NOT NULL,
    `emp_email` VARCHAR(50) NOT NULL,
    `emp_mobile` BIGINT NOT NULL,
    `company_id` INTEGER NULL,
    `designation` VARCHAR(255) NULL,
    `created_at` TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),

    INDEX `company_id`(`company_id`),
    INDEX `emp_id`(`emp_id`),
    INDEX `idx_employer_designation`(`designation`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `employerplans` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `emp_id` INTEGER NOT NULL,
    `plan_id` INTEGER NOT NULL,
    `start_date` TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    `end_date` TIMESTAMP(0) NULL,
    `creditsLeft` INTEGER NULL DEFAULT 0,

    INDEX `idx_employerplans_dates`(`start_date`, `end_date`),
    INDEX `idx_employerplans_empid`(`emp_id`),
    INDEX `idx_employerplans_planid`(`plan_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `employerprofiles` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `emp_id` INTEGER NOT NULL,
    `video_resume_id` INTEGER NULL,
    `status` ENUM('shortlisted', 'unlocked') NOT NULL,
    `shortlisted_at` TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    `unlocked_at` TIMESTAMP(0) NULL,

    INDEX `idx_employerprofiles_candid`(`video_resume_id`),
    INDEX `idx_employerprofiles_dates`(`shortlisted_at`, `unlocked_at`),
    INDEX `idx_employerprofiles_empid`(`emp_id`),
    INDEX `idx_employerprofiles_status`(`status`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `job_descriptions` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `employer_id` INTEGER NOT NULL,
    `suggested_profiles` JSON NOT NULL,
    `JobDescription` JSON NOT NULL,
    `timestamp` DATETIME(0) NULL DEFAULT CURRENT_TIMESTAMP(0),

    INDEX `fk_employer_job_descriptions`(`employer_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `jobseeker` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `cand_id` VARCHAR(20) NOT NULL,
    `cand_name` VARCHAR(255) NOT NULL,
    `cand_mobile` VARCHAR(15) NULL,
    `cand_email` VARCHAR(100) NOT NULL,
    `marks` LONGTEXT NULL,
    `gender` ENUM('male', 'female', 'other') NOT NULL,
    `languages_known` TEXT NULL,
    `cand_skills` TEXT NULL,
    `profile_picture` VARCHAR(255) NULL,
    `stripped_resume` TEXT NULL,
    `preferred_location` VARCHAR(255) NULL,
    `created_at` TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),

    UNIQUE INDEX `jobseeker_cand_id_key`(`cand_id`),
    INDEX `cand_id`(`cand_id`),
    INDEX `fk_cand_email`(`cand_email`),
    INDEX `idx_jobseeker_location`(`preferred_location`),
    INDEX `idx_jobseeker_mobile`(`cand_mobile`),
    INDEX `idx_jobseeker_name`(`cand_name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `jobseekerplans` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `cand_id` INTEGER NOT NULL,
    `plan_id` INTEGER NOT NULL,
    `start_date` TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    `end_date` TIMESTAMP(0) NULL,
    `is_profile_forwarded` BOOLEAN NULL DEFAULT false,
    `is_profile_promoted` BOOLEAN NULL DEFAULT false,
    `review_count` INTEGER NULL DEFAULT 0,
    `credits` INTEGER NULL DEFAULT 0,

    INDEX `idx_jobseekerplans_candid`(`cand_id`),
    INDEX `idx_jobseekerplans_dates`(`start_date`, `end_date`),
    INDEX `idx_jobseekerplans_planid`(`plan_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;


-- CreateTable
CREATE TABLE `plans` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `plan_name` VARCHAR(255) NOT NULL,
    `plan_description` TEXT NULL,
    `plan_price` DECIMAL(10, 2) NOT NULL,
    `plan_duration_days` INTEGER NOT NULL,
    `role` ENUM('js', 'emp') NULL,
    `credits_assigned` INTEGER NULL,
    `features` TEXT NULL,
    `created_at` TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    `updated_at` TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),

    INDEX `idx_plans_name`(`plan_name`),
    INDEX `idx_plans_role`(`role`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `suggestedjobs` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `title` VARCHAR(50) NOT NULL,
    `company` VARCHAR(200) NOT NULL,
    `openings` INTEGER NOT NULL,
    `url` VARCHAR(200) NOT NULL,
    `image` VARCHAR(100) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `email` VARCHAR(255) NOT NULL,
    `password` VARCHAR(255) NOT NULL,
    `role` ENUM('jobseeker', 'employer') NOT NULL,
    `created_at` TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    `updated_at` TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),

    UNIQUE INDEX `email`(`email`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `videoprofile` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `cand_id` VARCHAR(20) NOT NULL,
    `video_profile_id` BIGINT NOT NULL,
    `role` VARCHAR(255) NOT NULL,
    `skills` TEXT NULL,
    `job_type` ENUM('startup', 'mid_range', 'mnc') NULL,
    `experience_range` ENUM('0-1', '2-3', '3-5') NULL,
    `salary` LONGTEXT NULL,
    `questions` LONGTEXT NULL,
    `video_url` VARCHAR(255) NULL,
    `score` LONGTEXT NULL,
    `status` ENUM('active', 'inactive', 'started', 'notsubmitted') NOT NULL,
    `created_at` TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),

    INDEX `fk_cand_id`(`cand_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `roles` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `role_name` VARCHAR(255) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `skills` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `skill_name` VARCHAR(255) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `analytics` ADD CONSTRAINT `fk_employer` FOREIGN KEY (`employer_id`) REFERENCES `employer`(`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `analytics` ADD CONSTRAINT `fk_profile` FOREIGN KEY (`profile_id`) REFERENCES `videoprofile`(`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `employer` ADD CONSTRAINT `employer_ibfk_1` FOREIGN KEY (`emp_id`) REFERENCES `user`(`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `employer` ADD CONSTRAINT `employer_ibfk_2` FOREIGN KEY (`company_id`) REFERENCES `company`(`id`) ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `employerplans` ADD CONSTRAINT `employerplans_ibfk_1` FOREIGN KEY (`emp_id`) REFERENCES `employer`(`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `employerplans` ADD CONSTRAINT `employerplans_ibfk_2` FOREIGN KEY (`plan_id`) REFERENCES `plans`(`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `employerprofiles` ADD CONSTRAINT `employerprofiles_ibfk_1` FOREIGN KEY (`emp_id`) REFERENCES `employer`(`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `employerprofiles` ADD CONSTRAINT `fk_video_resume_id` FOREIGN KEY (`video_resume_id`) REFERENCES `videoprofile`(`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `job_descriptions` ADD CONSTRAINT `fk_employer_job_descriptions` FOREIGN KEY (`employer_id`) REFERENCES `employer`(`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `jobseeker` ADD CONSTRAINT `fk_cand_email` FOREIGN KEY (`cand_email`) REFERENCES `user`(`email`) ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `jobseekerplans` ADD CONSTRAINT `jobseekerplans_ibfk_1` FOREIGN KEY (`cand_id`) REFERENCES `jobseeker`(`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `jobseekerplans` ADD CONSTRAINT `jobseekerplans_ibfk_2` FOREIGN KEY (`plan_id`) REFERENCES `plans`(`id`) ON DELETE CASCADE ON UPDATE NO ACTION;


-- AddForeignKey
ALTER TABLE `videoprofile` ADD CONSTRAINT `fk_cand_id` FOREIGN KEY (`cand_id`) REFERENCES `jobseeker`(`cand_id`) ON DELETE CASCADE ON UPDATE NO ACTION;
