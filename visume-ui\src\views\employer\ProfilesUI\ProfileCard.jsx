// ProfileCard.jsx
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { HiBriefcase, HiHeart, HiLocationMarker, HiOutlineHeart, HiOutlineLocationMarker, HiVideoCamera } from "react-icons/hi";
import Cookies from "js-cookie";
import toast from "react-hot-toast";

const ProfileCard = ({
  keyValue,
  experience_range,
  shortListedProfiles,
  score,
  video_profile_id,
  candidateDetails,
  role,
  id,
  onShortlist,
  isShortlisted,
  isLoading,
  cand_id,
  skills,
  isSkeleton
}) => {
  const skillsArray = skills ? skills.split(",").map((skill) => skill.trim()) : [];
  const [imageError, setImageError] = useState(false);
  const emp_id = Cookies.get("employerId");
  const [hasViewed, setHasViewed] = useState(false);
  const [hasClicked, setHasClicked] = useState(false);

  const navigate = useNavigate();

  const profileClick = async () => {
    const currentUrl = window.location.href;
    localStorage.setItem("previousUrl", currentUrl);
    navigate(`/profile/${video_profile_id}`);
    if (!hasClicked) {
      try {
        if (!emp_id) {
          return toast.error("You need to be an employer to shortlist profiles");
        }
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/analytics`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              employer_id: emp_id,
              profile_id: id,
              interaction_type: "click",
            }),
          }
        );
        if (!response.ok) {
          const msg = await response.json();
          if (
            msg.message ===
            "Duplicate interaction: the same interaction type already exists for this employer and profile."
          ) {
            toast(msg.message);
            return;
          } else {
            toast.error(msg.message);
            throw new Error(`HTTP error! status: ${msg.message}`);
          }
        }
        await response.json();
      } catch (err) {
        console.log(err);
      } finally {
        setHasClicked(true);
      }
    }
  };

  return (
    <div
      key={keyValue}
      className={`flex flex-col justify-between rounded-2xl ${
        isSkeleton
          ? 'bg-gray-50 animate-pulse'
          : 'bg-gradient-to-br from-white via-gray-50 to-gray-100'
      } border border-gray-200 shadow-xl p-5 transition-all duration-200 hover:shadow-2xl`}
    >
      {/* Header Row: Profile Image, Name, Role */}
      <div className="flex items-center gap-4">
        {/* Profile Image */}
        <div
          className={`h-16 w-16 flex-shrink-0 ${!isSkeleton && 'cursor-pointer'} rounded-full border-4 border-white shadow-md ${isSkeleton ? 'bg-gray-300' : 'bg-gray-200'} overflow-hidden`}
          onClick={!isSkeleton ? profileClick : undefined}
        >
          {imageError ? (
            <div className="flex h-full w-full items-center justify-center rounded-full bg-brand-600 text-2xl font-semibold text-white">
              {candidateDetails[0].cand_name[0].toUpperCase()}
            </div>
          ) : (
            <img
              className="h-full w-full rounded-full object-cover"
              src={`${import.meta.env.VITE_APP_HOST}/${candidateDetails[0].profile_picture}`}
              alt={candidateDetails[0].cand_name}
              onError={() => setImageError(true)}
            />
          )}
        </div>
        {/* Name and Role */}
        <div className="flex flex-grow flex-col">
          <div
            onClick={profileClick}
            className="flex cursor-pointer items-start justify-between"
          >
            <div className="flex flex-wrap items-center gap-2">
              <h2 className="text-lg font-bold text-gray-900 flex items-center gap-1">
                {candidateDetails[0].cand_name}
                <span className="ml-1 text-indigo-500">
                  <HiBriefcase />
                </span>
              </h2>
              <span className="inline-flex items-center gap-1 rounded-full bg-purple-100 px-3 py-0.5 text-xs font-semibold text-purple-800 border border-purple-200">
                {role}
              </span>
              <span className="inline-flex items-center gap-1 rounded-full bg-blue-50 px-3 py-0.5 text-xs font-semibold text-blue-700 border border-blue-200">
                <span className="text-blue-400">
                  <HiBriefcase />
                </span>
                Exp: {experience_range}
              </span>
            </div>
            <span className="flex h-7 w-7 items-center justify-center rounded-full bg-gradient-to-br from-purple-200 to-indigo-200 text-base font-bold text-indigo-700 shadow">
              {Math.round(JSON.parse(score)?.score?.Overall_Score) || 0}
            </span>
          </div>
          {/* Location and Salary */}
          <div className="mt-1 flex items-center text-xs text-gray-500 gap-2">
            <span className="flex items-center gap-1">
              <span className="font-semibold text-green-600">₹12-14 LPA</span>
            </span>
            <span className="mx-1">&#183;</span>
            <span className="flex items-center gap-1">
              <HiOutlineLocationMarker className="text-indigo-400" />
              <span className="ml-1 truncate">
                {candidateDetails[0].preferred_location.startsWith("[")
                  ? JSON.parse(candidateDetails[0].preferred_location).join(", ")
                  : candidateDetails[0].preferred_location}
              </span>
            </span>
          </div>
        </div>
      </div>
      {/* Skills */}
      <div className="mt-4 flex flex-wrap gap-2">
        {skillsArray.slice(0, 4).map((skill, index) => (
          <span
            key={index}
            className="inline-flex items-center gap-1 rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 px-3 py-1 text-xs font-semibold text-indigo-700 border border-indigo-200 shadow-sm"
          >
            <svg className="w-3 h-3 text-indigo-400" fill="currentColor" viewBox="0 0 20 20">
              <circle cx="10" cy="10" r="10" />
            </svg>
            {skill}
          </span>
        ))}
        {skillsArray.length > 6 && (
          <span className="inline-flex items-center gap-1 rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 px-3 py-1 text-xs font-semibold text-indigo-700 border border-indigo-200 shadow-sm">
            +{skillsArray.length - 6} more
          </span>
        )}
      </div>
      {/* Action Buttons */}
      {/* Match Details */}
      <div className="mt-4 flex items-center gap-2 text-xs">
        {JSON.parse(score)?.score?.Experience_Match > 70 && (
          <span className="inline-flex items-center gap-1 rounded-full bg-yellow-100 px-2 py-0.5 font-medium text-yellow-700">
            Experience Match
          </span>
        )}
      </div>

      {/* Action Buttons */}
      <div className="mt-4 flex gap-3 border-t border-gray-200 pt-4">
        <button
          onClick={!isSkeleton ? profileClick : undefined}
          className={`flex flex-1 items-center justify-center gap-2 rounded-lg border ${
            isSkeleton
              ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-default'
              : 'border-indigo-200 bg-white text-indigo-700 hover:bg-indigo-50 hover:text-indigo-900'
          } px-3 py-2 text-sm font-semibold shadow-sm transition-colors`}
          disabled={isSkeleton}
        >
          <HiVideoCamera className="text-lg" />
          View Profile
        </button>
        <button
          className={`flex flex-1 items-center justify-center gap-2 rounded-lg px-3 py-2 text-sm font-semibold shadow-sm transition-colors ${
            isShortlisted || shortListedProfiles?.includes(video_profile_id)
              ? "bg-indigo-500 text-white"
              : "border border-gray-300 bg-white text-gray-700 hover:border-red-400 hover:text-red-500"
          }`}
          onClick={() =>
            !isShortlisted &&
            !shortListedProfiles?.includes(video_profile_id) &&
            onShortlist(id, cand_id)
          }
          disabled={isLoading}
        >
          {isLoading ? (
            <svg
              className="text-current h-4 w-4 animate-spin"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
                fill="none"
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
          ) : isShortlisted ||
            shortListedProfiles?.includes(video_profile_id) ? (
            <>
              <HiHeart className="fill-current text-base" />
              Shortlisted
            </>
          ) : (
            <>
              <HiOutlineHeart className="text-base" />
              Shortlist
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default ProfileCard;