// SearchBar.jsx
import React, { useEffect, useState } from "react";
import { Search, Filter } from "lucide-react";
import { HiBriefcase, HiLocationMarker } from "react-icons/hi";
import AdvancedSearchModal from "../components/AdvancedSearchModal";
import { useLocation, useNavigate } from "react-router-dom";

const SearchBar = ({
  setProfiles,
  loading,
  setLoading,
  setSetShowLoadMore,
  getAllProfiles,
  setOldProfiles,
  oldProfiles,
  setViewInteraction,
}) => {
  const [location, setLocation] = useState("");
  const [role, setRole] = useState("");
  const [skills, setSkills] = useState("");
  const [focusedInput, setFocusedInput] = useState(null);
  const [selectedSkills, setSelectedSkills] = useState([]);
  const [showAdvancedModal, setShowAdvancedModal] = useState(false);
  const [advancedFilters, setAdvancedFilters] = useState({
    experience: "",
    expectedSalary: "",
    currentSalary: "",
    score: "",
  });
  const [countAdvanceFilters, setCountAdvanceFilters] = useState(0);

  const [roleSuggestion, setRoleSuggestion] = useState([]);
  const [skillSuggestion, setSkillSuggestion] = useState([]);
  const [locationSuggestion, setLocationSuggestion] = useState([]);

  const jobRoles = [
    "Software Developer/Engineer",
    "Java Developer",
    "Frontend Developer",
    "Backend Developer",
    "Full Stack Developer",
    "DevOps Engineer",
  ];

  const skillsList = [
    "JavaScript",
    "Python",
    "React",
    "Node.js",
    "CSS",
    "HTML",
    "Tailwind CSS",
    "Django",
    "Java",
    "Spring MVC",
  ];

  const locations = ["Bangalore", "Delhi", "Mumbai", "Hyderabad"];

  const query = new URLSearchParams(useLocation().search);
  const navigate = useNavigate();

  const handleAddSkill = (skill) => {
    if (skill && !selectedSkills.includes(skill)) {
      setSelectedSkills([...selectedSkills, skill]);
      setSkills("");
    }
  };

  const handleRemoveSkill = (skill) => {
    setSelectedSkills(selectedSkills.filter((s) => s !== skill));
  };

  const handleSearch = async () => {
    try {
      if (
        !location.trim() &&
        !role.trim() &&
        !selectedSkills.length &&
        !Object.values(advancedFilters).some((value) => value)
      ) {
        navigate(`filterCandidate`);
        setProfiles([]);
        getAllProfiles(1);
        return;
      }

      setLoading(true);
      const queryParams = new URLSearchParams({
        ...(location && { preferred_location: location }),
        ...(role && { role }),
        ...(selectedSkills.length && {
          selectedSkills: JSON.stringify(selectedSkills),
        }),
        shortlisted: "false",
        ...(advancedFilters.experience && {
          experience: advancedFilters.experience,
        }),
        ...(advancedFilters.expectedSalary && {
          expected_salary: advancedFilters.expectedSalary,
        }),
        ...(advancedFilters.currentSalary && {
          current_salary: advancedFilters.currentSalary,
        }),
        ...(advancedFilters.score && { score: advancedFilters.score }),
      });

      const data = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/filterCandidate?${queryParams.toString()}`
      );

      const res = await data.json();
      setProfiles(res.candidateProfiles);
      setOldProfiles((prev) => {
        const updatedProfiles = [...prev, ...res.candidateProfiles];
        return updatedProfiles.slice(-10);
      });
      navigate(`filterCandidate?${queryParams.toString()}`);
      setViewInteraction((prev) => prev + 1);
      setSetShowLoadMore(false);
    } catch (err) {
      console.error("Error fetching candidate profiles:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    handleSearch();
    // eslint-disable-next-line
  }, [advancedFilters, selectedSkills]);

  useEffect(() => {
    let count = 0;
    for (const key in advancedFilters) {
      if (advancedFilters[key].trim()) {
        count += 1;
      }
    }
    setCountAdvanceFilters(count);
  }, [advancedFilters]);

  useEffect(() => {
    const preferredLocation = query.get("preferred_location") || "";
    const roleParam = query.get("role") || "";
    const experience = query.get("experience") || "";
    const expectedSalary = query.get("expected_salary") || "";
    const currentSalary = query.get("current_salary") || "";
    const score = query.get("score") || "";
    const skills = query.get("selectedSkills") || "[]";
    const decodedSkills = JSON.parse(decodeURIComponent(skills));

    setLocation(preferredLocation);
    setRole(roleParam);
    setAdvancedFilters({
      experience,
      expectedSalary,
      currentSalary,
      score,
    });
    setSelectedSkills(decodedSkills);
    // eslint-disable-next-line
  }, []);

  useEffect(() => {
    const advFilter = Object.values(advancedFilters).some((value) => value);
    if (
      !selectedSkills.length &&
      !role &&
      !location &&
      !advFilter &&
      oldProfiles.length
    ) {
      navigate(`filterCandidate`);
      setProfiles((prev) => {
        const updatedProfiles = [...prev, ...oldProfiles].slice(-10);
        const uniqueProfiles = [
          ...new Map(updatedProfiles.map((item) => [item.id, item])).values(),
        ];
        return uniqueProfiles.reverse();
      });
    }
    // eslint-disable-next-line
  }, [selectedSkills, role, location, advancedFilters, oldProfiles]);

  return (
    <div className="relative mx-auto mb-5 w-full max-w-4xl px-3">
      <div className="flex w-full flex-col gap-2 rounded-lg bg-white p-1 shadow-md sm:p-3">
        <div className="flex flex-col gap-2 sm:flex-row sm:items-start">
          {/* Inputs Container */}
          <div className="flex-1 space-y-2 sm:grid sm:grid-cols-3 sm:gap-2 sm:space-y-0">
            {/* Location Input */}
            <div className="relative flex items-center rounded-md px-3 focus-within:ring-2 focus-within:ring-indigo-500">
              <HiLocationMarker
                size={18}
                className={focusedInput === "location" ? "text-indigo-500" : "text-gray-400"}
              />
              <input
                type="text"
                placeholder="Location"
                value={location}
                onChange={(e) => {
                  setLocation(e.target.value);
                  if (e.target.value) {
                    const locationSuggest = locations.filter((val) =>
                      val.toLowerCase().includes(e.target.value.toLowerCase())
                    );
                    setLocationSuggestion(locationSuggest);
                  } else {
                    setLocationSuggestion([]);
                  }
                }}
                className="bg-transparent w-full py-2 pl-2 text-sm sm:text-base focus:outline-none "
                onFocus={() => setFocusedInput("location")}
                onBlur={() => setFocusedInput(null)}
              />
            </div>
            {locationSuggestion && locationSuggestion.length ? (
              <div className="absolute top-20 z-50 flex max-h-[300px] w-[250px] flex-col overflow-y-auto rounded-lg border border-gray-300 bg-white shadow-lg">
                {locationSuggestion.slice(0, 10).map((e, index) => (
                  <span
                    onClick={() => {
                      setLocation(e);
                      setLocationSuggestion([]);
                    }}
                    key={index}
                    className="hover:text-black cursor-pointer rounded-t-md px-4 py-2 text-gray-800 transition-all duration-200 hover:bg-gray-100"
                  >
                    {e}
                  </span>
                ))}
              </div>
            ) : undefined}
            {/* Role Input */}
            <div className="relative flex items-center rounded-md px-3 focus-within:ring-2 focus-within:ring-indigo-500">
              <HiBriefcase
                size={18}
                className={focusedInput === "role" ? "text-indigo-500" : "text-gray-400"}
              />
              <input
                type="text"
                placeholder="Role"
                value={role}
                onChange={(e) => {
                  setRole(e.target.value);
                  if (e.target.value) {
                    const roleSuggest = jobRoles.filter((val) =>
                      val.toLowerCase().includes(e.target.value.toLowerCase())
                    );
                    setRoleSuggestion(roleSuggest);
                  } else {
                    setRoleSuggestion([]);
                  }
                }}
                className="bg-transparent w-full py-2 pl-2 text-sm focus:outline-none sm:text-base"
                onFocus={() => setFocusedInput("role")}
                onBlur={() => setFocusedInput(null)}
              />
            </div>
            {roleSuggestion && roleSuggestion.length ? (
              <div className="absolute left-[25%] top-20 z-50 flex max-h-[300px] w-[250px] flex-col overflow-y-auto rounded-lg border border-gray-300 bg-white shadow-lg">
                {roleSuggestion.slice(0, 10).map((e, index) => (
                  <span
                    onClick={() => {
                      setRole(e);
                      setRoleSuggestion([]);
                    }}
                    key={index}
                    className="hover:text-black cursor-pointer rounded-t-md px-4 py-2 text-gray-800 transition-all duration-200 hover:bg-gray-100"
                  >
                    {e}
                  </span>
                ))}
              </div>
            ) : undefined}
            {/* Skills Input */}
            <div className="relative flex items-center rounded-md px-3 focus-within:ring-2 focus-within:ring-indigo-500">
              <Filter
                size={18}
                className={focusedInput === "skills" ? "text-indigo-500" : "text-gray-400"}
              />
              <input
                type="text"
                placeholder="Skills"
                value={skills}
                onChange={(e) => {
                  setSkills(e.target.value);
                  if (e.target.value) {
                    const skillSuggest = skillsList.filter((val) =>
                      val.toLowerCase().includes(e.target.value.toLowerCase())
                    );
                    setSkillSuggestion(skillSuggest);
                  } else {
                    setSkillSuggestion([]);
                  }
                }}
                className="bg-transparent w-full py-2 pl-2 text-sm focus:outline-none sm:text-base"
                onFocus={() => setFocusedInput("skills")}
                onBlur={() => setFocusedInput(null)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleAddSkill(skills);
                  }
                }}
              />
            </div>
            {skillSuggestion && skillSuggestion.length ? (
              <div className="absolute left-[50%] top-20 z-50 flex max-h-[300px] w-[250px] flex-col overflow-y-auto rounded-lg border border-gray-300 bg-white shadow-lg">
                {skillSuggestion.slice(0, 10).map((e, index) => (
                  <span
                    onClick={() => {
                      setSkills(e);
                      setSkillSuggestion([]);
                    }}
                    key={index}
                    className="hover:text-black cursor-pointer rounded-t-md px-4 py-2 text-gray-800 transition-all duration-200 hover:bg-gray-100"
                  >
                    {e}
                  </span>
                ))}
              </div>
            ) : undefined}
          </div>
          {/* Buttons Container */}
          <div className="flex h-10 gap-2 sm:ml-2">
            <button
              onClick={() => setShowAdvancedModal(true)}
              className="relative flex flex-1 items-center justify-center gap-1 rounded-md border border-indigo-500 px-3 py-1.5 text-xs font-medium text-indigo-500 transition-colors hover:bg-indigo-50 sm:flex-initial"
            >
              <Filter size={16} />
              Filters
              {countAdvanceFilters > 0 && (
                <span className="absolute right-1 top-0 flex h-5 w-5 -translate-y-1/2 translate-x-1/2 transform items-center justify-center rounded-full bg-red-500 text-white">
                  {countAdvanceFilters}
                </span>
              )}
            </button>
            <button
              onClick={loading ? undefined : handleSearch}
              className="flex flex-1 items-center justify-center gap-1 rounded-md bg-indigo-500 px-3 py-1.5 text-xs font-medium text-white transition-colors hover:bg-indigo-600 sm:flex-initial"
            >
              {loading ? (
                <svg
                  className="h-4 w-4 animate-spin text-white"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                    fill="none"
                  />
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
              ) : (
                <>
                  <Search size={16} />
                  Search
                </>
              )}
            </button>
          </div>
        </div>
        {/* Selected Skills */}
        {selectedSkills.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {selectedSkills.map((skill) => (
              <span
                key={skill}
                className="flex items-center rounded-full bg-indigo-100 px-3 py-1 text-xs text-indigo-800"
              >
                {skill}
                <button
                  className="ml-2 text-indigo-600 hover:text-indigo-800"
                  onClick={() => handleRemoveSkill(skill)}
                >
                  ×
                </button>
              </span>
            ))}
          </div>
        )}
      </div>
      <div className="mt-2 flex items-center justify-start gap-2">
        {Object.entries(advancedFilters)
          .filter(([key, value]) => value)
          .map(([key, value], index) => (
            <div
              key={index}
              className="relative flex items-center space-x-2 rounded-full bg-brand-200 px-3 py-1 text-white"
            >
              <span className="px-2">{`${key}: ${value}`}</span>
              <button
                type="button"
                className="absolute right-0 top-0 flex h-3 w-3 items-center justify-center rounded-full bg-red-500 text-sm text-white hover:bg-red-700"
                onClick={() => {
                  const newFilters = { ...advancedFilters };
                  newFilters[key] = "";
                  setAdvancedFilters(newFilters);
                }}
              >
                &times;
              </button>
            </div>
          ))}
      </div>
      <AdvancedSearchModal
        isOpen={showAdvancedModal}
        onClose={() => setShowAdvancedModal(false)}
        advancedFilters={advancedFilters}
        setAdvancedFilters={setAdvancedFilters}
        onApplyFilters={(filters) => setAdvancedFilters(filters)}
      />
    </div>
  );
};

export default SearchBar;