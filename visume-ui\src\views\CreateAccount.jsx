import React, { useState, useRef, useEffect } from "react";
import {
  Briefcase,
  ChartBar,
  Trophy,
  Upload,
  Users,
  X,
  Check,
  FileText,
} from "lucide-react";
import LogoImage from "assets/img/Visume-logo-icon.png";
import videoRes from "assets/img/videores-illustration.png";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import Cookies from "js-cookie";
import useEmailValidation from "../hooks/useEmailValidation";

import { useLocation } from "react-router-dom";

const CreateAccount = () => {
  const location = useLocation();
  const googleData = location.state || {};
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);

  // Profile picture state
  const [profilePic, setProfilePic] = useState(null);
  const [profilePicPreview, setProfilePicPreview] = useState(null);
  const [profilePicError, setProfilePicError] = useState("");
  const profilePicInputRef = useRef(null);
  const [imageError, setImageError] = useState(false);

  // Remove profile picture handler
  const handleRemoveProfilePic = () => {
    setProfilePic(null);
    setProfilePicPreview(null);
    setProfilePicError("");
    if (profilePicInputRef.current) {
      profilePicInputRef.current.value = "";
    }
  };

  // ... (other state and functions remain unchanged)
  const jsregcookie = (data) => {
    const allCookies = Cookies.get(); // Get all cookies
    for (const cookieName in allCookies) {
      Cookies.remove(cookieName); // Remove each cookie
    }
    localStorage.clear();
    Cookies.set("candId", data.cand_id, { expires: 7 });
    Cookies.set("jstoken", data.token, { expires: 7 });
    Cookies.set("role", data.role, { expires: 7 });
  };

  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const isPdf = file.type === "application/pdf";
      if (isPdf) {
        setFormData({ ...formData, resume: file });
        setUploadedFile(file);
        setErrors({ ...errors, resume: "" });
      } else {
        setErrors({ ...errors, resume: "Please upload a valid PDF file." });
        setUploadedFile(null); // Clear the uploaded file if it's invalid
      }
    }
  };

  // Profile picture handler
  const handleProfilePicChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const validTypes = ["image/jpeg", "image/png", "image/jpg"];
      if (!validTypes.includes(file.type)) {
        setProfilePicError("Only JPG, JPEG, or PNG files are allowed.");
        setProfilePic(null);
        setProfilePicPreview(null);
        return;
      }
      if (file.size > 5 * 1024 * 1024) {
        setProfilePicError("File size must be less than 5MB.");
        setProfilePic(null);
        setProfilePicPreview(null);
        return;
      }
      setProfilePic(file);
      setProfilePicPreview(URL.createObjectURL(file));
      setProfilePicError("");
    }
  };

  const [formData, setFormData] = useState({
    fullName: googleData.name || "",
    email: googleData.email || "",
    password: "",
    mobile: "",
    gender: "",
    languages: [],
    locations: [],
    resume: null,
  });

  const [errors, setErrors] = useState({});
  const [selectedLanguages, setSelectedLanguages] = useState([]);
  const [selectedLocations, setSelectedLocations] = useState([]);
  const [isLanguageOpen, setIsLanguageOpen] = useState(false);
  const [isLocationOpen, setIsLocationOpen] = useState(false);

  // Email validation using custom hook
  const { emailValidation, validateEmail, resetValidation } = useEmailValidation();

  const languageDropdownRef = useRef(null);
  const locationDropdownRef = useRef(null);

  const [showSendOTPButton, setShowSendOTPButton] = useState(false);
  const [showOTPModal, setShowOTPModal] = useState(false);
  const [otp, setOTP] = useState(["", "", "", ""]);
  const [generatedOTP, setGeneratedOTP] = useState("");
  const [otpVerified, setOtpVerified] = useState(false);
  const [otpError, setOtpError] = useState("");

  const languageOptions = [
    "English",
    "Hindi",
    "Tamil",
    "Telugu",
    "Malayalam",
    "Kannada",
  ];
  const locationOptions = ["Bangalore", "Mumbai", "Delhi", "Hyderabad"];

  useEffect(() => {
    // Prefill profile picture preview if googleData.picture is present
    if (googleData.picture && !profilePicPreview) {
      setProfilePicPreview(googleData.picture);
    }

    // Prefill form fields only if needed to avoid infinite loop
    if (
      (googleData.name && formData.fullName !== googleData.name) ||
      (googleData.email && formData.email !== googleData.email)
    ) {
      setFormData((prev) => ({
        ...prev,
        fullName: googleData.name || prev.fullName,
        email: googleData.email || prev.email,
      }));
    }

    const handleClickOutside = (event) => {
      if (
        languageDropdownRef.current &&
        !languageDropdownRef.current.contains(event.target)
      ) {
        setIsLanguageOpen(false);
      }
      if (
        locationDropdownRef.current &&
        !locationDropdownRef.current.contains(event.target)
      ) {
        setIsLocationOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [
    googleData,
    googleData.picture,
    profilePicPreview,
    formData.fullName,
    formData.email,
  ]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setErrors({ ...errors, [name]: "" });

    // Debounced email validation
    if (name === "email") {
      validateEmail(value);
    }
  };

  const handlePhoneNumberChange = (e) => {
    const value = e.target.value.replace(/\D/g, "").slice(0, 10);
    setFormData({ ...formData, mobile: value });
    setShowSendOTPButton(value.length === 10);
    if (value.length !== 10) {
      setOtpVerified(false);
      setOtpError("");
    }
    setErrors({ ...errors, mobile: "" });
  };

  const handleSendOTPClick = (e) => {
    e.preventDefault();
    const newOTP = Math.floor(1000 + Math.random() * 9000).toString();
    setGeneratedOTP(newOTP);
    alert(`Your OTP is: ${newOTP}`); // In a real app, this would be sent via SMS
    setShowOTPModal(true);
    setOTP(["", "", "", ""]);
    setOtpVerified(false);
    setOtpError("");
  };

  const handleOTPChange = (index, value) => {
    const newOTP = [...otp];
    newOTP[index] = value.replace(/\D/g, "").slice(0, 1);
    setOTP(newOTP);

    if (value && index < 3) {
      const nextInput = document.getElementById(`otp-${index + 1}`);
      if (nextInput) nextInput.focus();
    }
  };

  const handleVerifyOTP = () => {
    const enteredOTP = otp.join("");
    if (enteredOTP === generatedOTP) {
      setOtpVerified(true);
      setOtpError("");
      setShowOTPModal(false);
    } else {
      setOtpVerified(false);
      setOtpError("Wrong OTP entered. Please try again.");
    }
  };

  const toggleLanguage = (language) => {
    const updatedLanguages = selectedLanguages.includes(language)
      ? selectedLanguages.filter((lang) => lang !== language)
      : [...selectedLanguages, language];
    setSelectedLanguages(updatedLanguages);
    setFormData({ ...formData, languages: updatedLanguages });
    setErrors({ ...errors, languages: "" });
  };

  const toggleLocation = (location) => {
    const updatedLocations = selectedLocations.includes(location)
      ? selectedLocations.filter((loc) => loc !== location)
      : [...selectedLocations, location];
    setSelectedLocations(updatedLocations);
    setFormData({ ...formData, locations: updatedLocations });
    setErrors({ ...errors, locations: "" });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const newErrors = {};

    // Validate required fields
    if (!formData.fullName) newErrors.fullName = "Full name is required";
    if (!formData.email) newErrors.email = "Email is required";
    if (!formData.password) newErrors.password = "Password is required";
    if (!formData.mobile) newErrors.mobile = "Mobile number is required";
    if (!formData.gender) newErrors.gender = "Gender is required";
    if (selectedLanguages.length === 0)
      newErrors.languages = "Please select at least one language";
    if (selectedLocations.length === 0)
      newErrors.locations = "Please select at least one location";
    if (!formData.resume) newErrors.resume = "Please upload your resume";
    // Profile picture is optional, but validate if present
    if (profilePic) {
      const validTypes = ["image/jpeg", "image/png", "image/jpg"];
      if (!validTypes.includes(profilePic.type)) {
        newErrors.profilePic = "Only JPG, JPEG, or PNG files are allowed.";
      }
      if (profilePic.size > 5 * 1024 * 1024) {
        newErrors.profilePic = "File size must be less than 5MB.";
      }
    }

    // Validate phone number verification
    if (!otpVerified) newErrors.mobile = "Please verify your phone number";

    // Validate email uniqueness
    if (emailValidation.isChecking) {
      newErrors.email = "Please wait while we check email availability";
    } else if (emailValidation.isValid === false) {
      newErrors.email = emailValidation.message || "Email validation failed";
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
    } else {
      // Form is valid, proceed with submission
      console.log("Form submitted:", formData);
      const finalFormData = {
        email: formData.email,
        password: formData.password,
        cand_name: formData.fullName,
        cand_mobile: formData.mobile,
        gender: formData.gender,
        languages_known: JSON.stringify(formData.languages),
        preferred_location: JSON.stringify(formData.locations),
        resume: formData.resume ? formData.resume : null,
        profile_pic: profilePic ? profilePic : null,
        profile_pic_url:
          !profilePic &&
          profilePicPreview &&
          typeof profilePicPreview === "string"
            ? profilePicPreview
            : null,
      };
      registerJobseeker(finalFormData);
    }
  };

  async function registerJobseeker(formData) {
    setIsProcessing(true);
    // Create a new FormData object
    const formPayload = new FormData();

    // Append the fields to the FormData object
    formPayload.append("email", formData.email);
    formPayload.append("password", formData.password);
    formPayload.append("cand_name", formData.cand_name);
    formPayload.append("cand_mobile", formData.cand_mobile);
    formPayload.append("gender", formData.gender);
    formPayload.append("languages_known", formData.languages_known);
    formPayload.append("preferred_location", formData.preferred_location);

    // Append the marks object as a JSON string
    formPayload.append("marks", JSON.stringify(formData.marks || {}));

    // Append files if they exist
    if (formData.resume) {
      formPayload.append("resume", formData.resume);
    }
    if (formData.profile_pic) {
      formPayload.append("profile_picture", formData.profile_pic);
    }
    if (formData.profile_pic_url) {
      formPayload.append("profile_pic_url", formData.profile_pic_url);
    }

    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/register-jobseeker`,
        {
          method: "POST",
          body: formPayload,
        }
      );

      const resdata = await response.json();
      console.log(resdata);

      if (response.ok) {
        jsregcookie(resdata);
        toast.success("Registered Successfully");
        navigate("/");
      } else if (response.status === 400) {
        throw new Error(resdata.message || "Invalid request");
      } else if (response.status === 409) {
        throw new Error(resdata.message || "Email already exists");
      } else if (response.status === 500) {
        throw new Error(resdata.message || "Server error");
      } else {
        throw new Error(resdata.message || "An unexpected error occurred");
      }
    } catch (error) {
      console.error("Registration error:", error);
      toast.error(error.message || "Something went wrong");
    } finally {
      setIsProcessing(false);
    }

    // window.location.href = "/candidate/dashboard";
  }
  const navigate = useNavigate();

  return (
    <div className="relative min-h-screen bg-gray-50 px-4 py-8">
      {/* Loading Overlay */}
      {isProcessing && (
        <div className="bg-black/30 fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm">
          <div className="w-full max-w-md rounded-xl bg-white p-8 text-center shadow-2xl">
            <div className="mb-4 flex justify-center">
              <div className="h-12 w-12 animate-spin rounded-full border-4 border-blue-200 border-t-blue-600"></div>
            </div>
            <h3 className="mb-2 text-lg font-semibold text-gray-900">
              Processing your resume
            </h3>
            <p className="text-sm text-gray-600">
              This may take a few moments. Please don't close this window.
            </p>
          </div>
        </div>
      )}
      <div className="mx-auto max-w-5xl">
        <div className="mb-6 flex items-center gap-2">
          <div className="flex items-center text-left font-poppins text-[24px] font-bold text-brand-500 dark:text-white">
            <img src={LogoImage} alt="Visume logo" className="mb-1 h-7 w-7" />
            <span className="ml-2 text-2xl font-bold">Visume.ai</span>
          </div>
          <div className="ml-auto text-sm text-gray-600">
            Already Registered?{" "}
            <a
              onClick={() => {
                navigate("/candidate/signIn");
              }}
              className="cursor-pointer text-brand-600"
            >
              Login here
            </a>
          </div>
        </div>

        <div className="grid gap-8 md:grid-cols-[2fr,1fr]">
          <div className="rounded-lg bg-white p-6 shadow-sm">
            <h2 className="mb-2 text-xl font-semibold">
              Create your Visume profile
            </h2>
            <p className="mb-6 text-sm text-gray-500">
              Create & share video resumes on India's No.1 AI Video Resume
              Platform
            </p>

            <form className="space-y-4" onSubmit={handleSubmit}>
              {/* Profile Picture Upload */}
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Profile Picture (optional)
                </label>
                <input
                  type="file"
                  accept="image/jpeg, image/png, image/jpg"
                  className="hidden"
                  id="profile-pic-upload-input"
                  onChange={handleProfilePicChange}
                  ref={profilePicInputRef}
                />
                <div
                  className="cursor-pointer rounded-md border-2 border-dashed p-4 text-center hover:border-brand-200"
                  onClick={() =>
                    document.getElementById("profile-pic-upload-input").click()
                  }
                >
                  {profilePicPreview && !imageError ? (
                    <div className="flex flex-col items-center">
                      <div className="relative">
                        <img
                          src={
                            typeof profilePicPreview === "string"
                              ? profilePicPreview
                              : URL.createObjectURL(profilePicPreview)
                          }
                          alt="Profile Preview"
                          className="mx-auto mb-2 h-24 w-24 rounded-full border object-cover"
                          onError={() => setImageError(true)}
                        />
                        <button
                          type="button"
                          aria-label="Remove profile picture"
                          className="absolute right-0 top-0 rounded-full border bg-white p-1 shadow hover:bg-gray-100"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemoveProfilePic();
                            setImageError(false);
                          }}
                        >
                          <X className="h-5 w-5 text-gray-500" />
                        </button>
                      </div>
                      <span className="text-sm text-gray-600">
                        {profilePic?.name}
                      </span>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center">
                      <div className="relative">
                        {/* Inline SVG shadow avatar as default */}
                        <svg
                          viewBox="0 0 96 96"
                          width="96"
                          height="96"
                          className="mx-auto mb-2 h-24 w-24 rounded-full border object-cover"
                          aria-label="Default Profile"
                        >
                          <circle cx="48" cy="48" r="48" fill="#e5e7eb" />
                          <ellipse
                            cx="48"
                            cy="40"
                            rx="20"
                            ry="20"
                            fill="#cbd5e1"
                          />
                          <ellipse
                            cx="48"
                            cy="78"
                            rx="28"
                            ry="14"
                            fill="#d1d5db"
                          />
                        </svg>
                      </div>
                      <p className="mt-1 text-sm text-gray-600">
                        Upload a profile picture (JPG, JPEG, PNG, Max 5MB)
                      </p>
                    </div>
                  )}
                </div>
                {(profilePicError || errors.profilePic) && (
                  <p className="mt-1 text-sm text-red-500">
                    {profilePicError || errors.profilePic}
                  </p>
                )}
              </div>

              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Full name<span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="fullName"
                  placeholder="What is your name?"
                  className="w-full rounded-md border p-3 text-gray-600 focus:outline-none focus:ring-2 focus:ring-brand-500"
                  value={formData.fullName}
                  onChange={handleInputChange}
                  required
                />
                {errors.fullName && (
                  <p className="mt-1 text-sm text-red-500">{errors.fullName}</p>
                )}
              </div>

              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Email ID<span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  name="email"
                  placeholder="Tell us your Email ID"
                  className={`w-full rounded-md p-3 text-gray-600 focus:outline-none focus:ring-2 ${
                    emailValidation.isChecking
                      ? "border border-blue-300 focus:ring-blue-500"
                      : emailValidation.isValid === null
                      ? "border border-gray-300 focus:ring-brand-500"
                      : emailValidation.isValid
                      ? "border border-green-500 focus:ring-green-500"
                      : "border border-red-500 focus:ring-red-500"
                  }`}
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                />
                {/* Email validation feedback */}
                {emailValidation.message && (
                  <p className={`mt-1 text-sm ${
                    emailValidation.isChecking
                      ? "text-blue-600"
                      : emailValidation.isValid
                      ? "text-green-600"
                      : "text-red-500"
                  }`}>
                    {emailValidation.isChecking && (
                      <span className="inline-flex items-center">
                        <svg className="animate-spin -ml-1 mr-2 h-3 w-3 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      </span>
                    )}
                    {emailValidation.message}
                  </p>
                )}
                {/* Existing form validation errors (preserved) */}
                {errors.email && (
                  <p className="mt-1 text-sm text-red-500">{errors.email}</p>
                )}
                <p className="mt-1 text-xs text-gray-500">
                  Employers will contact you on this email
                </p>
              </div>

              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Password<span className="text-red-500">*</span>
                </label>
                <input
                  type="password"
                  name="password"
                  placeholder="Minimum 6 characters"
                  className="w-full rounded-md border p-3 text-gray-600 focus:outline-none focus:ring-2 focus:ring-brand-500"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                  minLength={6}
                />
                {errors.password && (
                  <p className="mt-1 text-sm text-red-500">{errors.password}</p>
                )}
              </div>

              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Mobile number<span className="text-red-500">*</span>
                </label>
                <div className="flex">
                  <span className="inline-flex items-center rounded-l-md border border-r-0 bg-gray-50 px-3 text-gray-500">
                    +91
                  </span>
                  <input
                    type="tel"
                    name="mobile"
                    maxLength="10"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    placeholder="Enter your mobile number"
                    className="w-full rounded-r-md border p-3 text-gray-600 focus:outline-none focus:ring-2 focus:ring-brand-500"
                    value={formData.mobile}
                    onChange={handlePhoneNumberChange}
                    required
                  />
                </div>
                {errors.mobile && (
                  <p className="mt-1 text-sm text-red-500">{errors.mobile}</p>
                )}
                <p className="mt-1 text-xs text-gray-500">
                  Employers will contact you on this number
                </p>
                {showSendOTPButton && !otpVerified && (
                  <button
                    onClick={handleSendOTPClick}
                    className="mt-2 rounded-md bg-brand-600 px-4 py-2 text-white hover:bg-brand-700"
                  >
                    Send OTP
                  </button>
                )}
                {otpVerified && (
                  <p className="mt-2 text-green-600">Phone number verified!</p>
                )}
              </div>

              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Gender<span className="text-red-500">*</span>
                </label>

                <div className="flex gap-4">
                  {["male", "female", "other"].map((gender) => (
                    <div
                      key={gender}
                      className={`flex cursor-pointer items-center rounded-md border p-2 hover:bg-gray-200 ${
                        formData.gender === gender
                          ? "border-gray-500 bg-gray-200"
                          : ""
                      }`}
                      onClick={() => {
                        setFormData({ ...formData, gender });
                        setErrors({ ...errors, gender: "" });
                      }}
                    >
                      <input
                        type="radio"
                        name="gender"
                        value={gender}
                        className="mr-2 hidden"
                        id={gender}
                        checked={formData.gender === gender}
                        onChange={() => {
                          setFormData({ ...formData, gender });
                          setErrors({ ...errors, gender: "" });
                        }}
                      />
                      <label htmlFor={gender} className="flex items-center">
                        <div
                          className={`h-6 w-6 rounded-full border-2 ${
                            gender === "male"
                              ? "border-blue-500"
                              : gender === "female"
                              ? "border-pink-500"
                              : "border-yellow-500"
                          } flex items-center justify-center ${
                            formData.gender === gender ? "bg-gray-300" : ""
                          }`}
                        >
                          {gender === "male" && <span>♂️</span>}
                          {gender === "female" && <span>♀️</span>}
                          {gender === "other" && <span>⚧️</span>}
                        </div>
                      </label>
                      <span className="ml-2">{gender}</span>
                    </div>
                  ))}
                </div>

                {errors.gender && (
                  <p className="mt-1 text-sm text-red-500">{errors.gender}</p>
                )}
              </div>

              <div className="flex gap-2">
                <div className="w-full" ref={languageDropdownRef}>
                  <label
                    htmlFor="language-select"
                    className="mb-1 block text-sm font-medium text-gray-700"
                  >
                    Languages known<span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <button
                      type="button"
                      id="language-select"
                      className="focus:border-primary focus:ring-primary w-full rounded-md border border-gray-300 bg-white px-4 py-2 text-left shadow-sm focus:outline-none focus:ring-2"
                      onClick={() => setIsLanguageOpen(!isLanguageOpen)}
                      aria-haspopup="listbox"
                      aria-expanded={isLanguageOpen}
                    >
                      {selectedLanguages.length > 0
                        ? `${selectedLanguages.length} selected`
                        : "Select languages"}
                      <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                        <svg
                          className="h-5 w-5 text-gray-400"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                          aria-hidden="true"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </span>
                    </button>
                    {isLanguageOpen && (
                      <ul
                        className="ring-black absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-opacity-5 focus:outline-none sm:text-sm"
                        role="listbox"
                        aria-labelledby="language-select"
                        tabIndex={-1}
                      >
                        {languageOptions.map((language) => (
                          <li
                            key={language}
                            className={`${
                              selectedLanguages.includes(language)
                                ? "bg-brand-100 text-brand-900"
                                : "text-gray-900"
                            } relative cursor-default select-none py-2  pl-3 pr-9`}
                            role="option"
                            aria-selected={selectedLanguages.includes(language)}
                            onClick={() => toggleLanguage(language)}
                          >
                            <span
                              className={`block truncate ${
                                selectedLanguages.includes(language)
                                  ? "font-semibold"
                                  : "font-normal"
                              }`}
                            >
                              {language}
                            </span>
                            {selectedLanguages.includes(language) && (
                              <span className="absolute inset-y-0 right-0 flex items-center pr-4 text-brand-600">
                                <Check className="h-5 w-5" aria-hidden="true" />
                              </span>
                            )}
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                  {selectedLanguages.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-2">
                      {selectedLanguages.map((lang) => (
                        <span
                          key={lang}
                          className="inline-flex items-center rounded-full bg-brand-100 px-2 py-1 text-xs font-medium text-brand-800"
                        >
                          {lang}
                          <button
                            type="button"
                            className="ml-1 inline-flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-full text-brand-400 hover:bg-brand-200 hover:text-brand-500 focus:bg-brand-500 focus:text-white focus:outline-none"
                            onClick={() => toggleLanguage(lang)}
                          >
                            <span className="sr-only">Remove {lang}</span>
                            <X className="h-3 w-3" />
                          </button>
                        </span>
                      ))}
                    </div>
                  )}
                  {errors.languages && (
                    <p className="mt-1 text-sm text-red-500">
                      {errors.languages}
                    </p>
                  )}
                </div>

                <div className="w-full" ref={locationDropdownRef}>
                  <label
                    htmlFor="location-select"
                    className="mb-1 block text-sm font-medium text-gray-700"
                  >
                    Preferred Locations<span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <button
                      type="button"
                      id="location-select"
                      className="focus:border-primary focus:ring-primary w-full rounded-md border border-gray-300 bg-white px-4 py-2 text-left shadow-sm focus:outline-none focus:ring-2"
                      onClick={() => setIsLocationOpen(!isLocationOpen)}
                      aria-haspopup="listbox"
                      aria-expanded={isLocationOpen}
                    >
                      {selectedLocations.length > 0
                        ? `${selectedLocations.length} selected`
                        : "Select locations"}
                      <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                        <svg
                          className="h-5 w-5 text-gray-400"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                          aria-hidden="true"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </span>
                    </button>
                    {isLocationOpen && (
                      <ul
                        className="ring-black absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-opacity-5 focus:outline-none sm:text-sm"
                        role="listbox"
                        aria-labelledby="location-select"
                        tabIndex={-1}
                      >
                        {locationOptions.map((location) => (
                          <li
                            key={location}
                            className={`${
                              selectedLocations.includes(location)
                                ? "bg-brand-100 text-brand-900"
                                : "text-gray-900"
                            } relative cursor-default select-none py-2 pl-3 pr-9`}
                            role="option"
                            aria-selected={selectedLocations.includes(location)}
                            onClick={() => toggleLocation(location)}
                          >
                            <span
                              className={`block truncate ${
                                selectedLocations.includes(location)
                                  ? "font-semibold"
                                  : "font-normal"
                              }`}
                            >
                              {location}
                            </span>
                            {selectedLocations.includes(location) && (
                              <span className="absolute inset-y-0 right-0 flex items-center pr-4 text-brand-600">
                                <Check className="h-5 w-5" aria-hidden="true" />
                              </span>
                            )}
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                  {selectedLocations.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-2">
                      {selectedLocations.map((loc) => (
                        <span
                          key={loc}
                          className="inline-flex items-center rounded-full bg-brand-100 px-2 py-1 text-xs font-medium text-brand-800"
                        >
                          {loc}
                          <button
                            type="button"
                            className="ml-1 inline-flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-full text-brand-400 hover:bg-brand-200 hover:text-brand-500 focus:bg-brand-500 focus:text-white focus:outline-none"
                            onClick={() => toggleLocation(loc)}
                          >
                            <span className="sr-only">Remove {loc}</span>
                            <X className="h-3 w-3" />
                          </button>
                        </span>
                      ))}
                    </div>
                  )}
                  {errors.locations && (
                    <p className="mt-1 text-sm text-red-500">
                      {errors.locations}
                    </p>
                  )}
                </div>
              </div>

              <div className="hover:text-brand-200">
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Resume Upload<span className="text-red-500">*</span>
                </label>
                <input
                  type="file"
                  accept=".pdf, .doc, .docx"
                  className="hidden"
                  onChange={handleFileUpload}
                  id="resume-upload-input"
                />
                <div
                  className="cursor-pointer rounded-md border-2 border-dashed p-4 text-center hover:border-brand-200"
                  onClick={() =>
                    document.getElementById("resume-upload-input").click()
                  }
                >
                  {uploadedFile ? (
                    <div className="flex items-center justify-center">
                      <FileText className="h-12 w-12 text-brand-200" />
                      <span className="ml-2 text-sm text-gray-600">
                        {uploadedFile.name}
                      </span>
                    </div>
                  ) : (
                    <>
                      <Upload className="mx-auto h-12 w-12 text-gray-400" />
                      <p className="mt-1 text-sm text-gray-600">
                        Upload your resume in PDF or DOC format
                      </p>
                      <p className="text-xs text-gray-500">
                        Supported formats: PDF, DOC, DOCX (Max 5 MB)
                      </p>
                    </>
                  )}
                </div>
                {errors.resume && (
                  <p className="mt-1 text-sm text-red-500">{errors.resume}</p>
                )}
              </div>

              <div className="flex items-center gap-2">
                <input type="checkbox" className="rounded text-brand-600" />
                <span className="text-sm text-gray-600">
                  Send me referrals & job opportunities via SMS, email, and
                  WhatsApp
                </span>
              </div>

              <div className="text-sm text-gray-500">
                By clicking Register, you agree to the{" "}
                <a href="#" className="text-brand-600">
                  Terms and Conditions
                </a>{" "}
                &{" "}
                <a href="#" className="text-brand-600">
                  Privacy Policy
                </a>
              </div>

              <button
                type="submit"
                className="w-full rounded-md bg-brand-600 py-3 font-medium text-white transition-colors hover:bg-brand-700"
              >
                Create Visume Profile
              </button>
            </form>
          </div>

          <div className="h-max rounded-lg bg-white p-6 shadow-sm">
            <div className="mb-6">
              <div className="mb-4 flex justify-center">
                <img
                  src={videoRes}
                  alt="Visume illustration"
                  className="w-64 rounded-lg"
                />
              </div>
              <h3 className="mb-6 text-center text-lg font-semibold text-gray-800">
                Create a Visume, get
              </h3>
              <ul className="space-y-4">
                <li className="flex items-center gap-3">
                  <div className="flex items-center justify-center rounded-full bg-blue-100">
                    <ChartBar className="h-4 w-4 text-blue-600" />
                  </div>
                  <span className="text-gray-700">
                    <strong className="font-medium">50% higher</strong> chance
                    of getting hired compared to traditional resumes
                  </span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="flex  items-center justify-center rounded-full bg-green-100">
                    <Users className="h-4 w-4 text-green-600" />
                  </div>
                  <span className="text-gray-700">
                    Direct profile forwarding to{" "}
                    <strong className="font-medium">500+ top recruiters</strong>{" "}
                    in your industry
                  </span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="flex items-center justify-center rounded-full bg-purple-100">
                    <Trophy className="h-4 w-4 text-purple-600" />
                  </div>
                  <span className="text-gray-700">
                    Stand out with{" "}
                    <strong className="font-medium">
                      professional video presentations
                    </strong>{" "}
                    showcasing your skills
                  </span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="flex  items-center justify-center rounded-full bg-orange-100">
                    <Briefcase className="h-4 w-4 text-orange-600" />
                  </div>
                  <span className="text-gray-700">
                    Access to{" "}
                    <strong className="font-medium">
                      premium job listings
                    </strong>{" "}
                    from Fortune 500 companies
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {showOTPModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-md">
          <div className="bg-black absolute inset-0 opacity-50"></div>
          <div className="relative z-50 w-full max-w-md rounded-lg bg-white p-6 shadow-xl">
            <button
              onClick={() => setShowOTPModal(false)}
              className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
            >
              <X className="h-6 w-6" />
            </button>
            <h3 className="mb-4 text-lg font-semibold">Enter OTP</h3>
            <div className="mb-4 flex justify-center space-x-2">
              {otp.map((digit, index) => (
                <input
                  key={index}
                  id={`otp-${index}`}
                  type="text"
                  inputMode="numeric"
                  maxLength={1}
                  value={digit}
                  onChange={(e) => handleOTPChange(index, e.target.value)}
                  className="h-12 w-12 rounded-md border border-gray-300 text-center text-lg shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                  aria-label={`OTP digit ${index + 1}`}
                />
              ))}
            </div>
            {otpError && (
              <p className="mb-4 text-center text-red-600">{otpError}</p>
            )}
            <div className="flex justify-between">
              <button
                onClick={handleVerifyOTP}
                className="rounded-md bg-brand-600 px-4 py-2 text-white hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2"
              >
                Verify OTP
              </button>
              <button
                onClick={handleSendOTPClick}
                className="text-brand-600 hover:text-brand-800 focus:underline focus:outline-none"
              >
                Resend OTP
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CreateAccount;
