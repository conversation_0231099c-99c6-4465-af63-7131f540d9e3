// Sidebar.jsx
import React from "react";
import { Star } from "lucide-react";

const Sidebar = ({ profileData, profiles }) => (
  <div className="max-w-full space-y-2 p-0  lg:col-span-2">
    <div className="sticky top-[80px] space-y-2">
      <div className="rounded-2xl bg-white p-4">
        <h2 className="mb-2 text-left  text-xl font-semibold">
          Similar profiles
        </h2>
        <ul className="space-y-2">
          {profiles.map((profile, index) => (
            <li
              key={index}
              className="flex items-center space-x-4 rounded-lg border border-b-gray-200 bg-white p-3"
            >
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-brand-700 object-cover text-2xl text-white shadow-sm">
                {profile.name[0]}
              </div>
              <div className="flex-grow">
                <h3 className="font-semibold">{profile.name}</h3>
                <p className="text-sm text-brand-600">{profile.role}</p>
              </div>
              <div className="flex items-center">
                <Star className="fill-current h-4 w-4 text-yellow-400" />
                <span className="ml-1 text-sm font-medium">{profile.rating}</span>
              </div>
            </li>
          ))}
        </ul>
        <button className="mt-2 w-full rounded-md bg-brand-600 py-2 text-white">
          Show All
        </button>
      </div>
    </div>
  </div>
);

export default Sidebar;