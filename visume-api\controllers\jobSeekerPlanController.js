const pool = require("../config/db"); // Assuming the pool is exported from the db file

// Create a new jobseeker plan
exports.createJobSeekerPlan = async (req, res) => {
  const { name, features } = req.body;

  // Validate required fields
  if (!name || !features) {
    return res.status(400).json({ message: "Name and features are required." });
  }

  try {
    // Get a connection from the pool
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res
          .status(500)
          .json({ message: "Failed to connect to the database." });
      }

      // SQL query to insert a new jobseeker plan
      const insertQuery = `
          INSERT INTO jobseekerplans (plan_name, plan_features)
          VALUES (?, ?);
        `;

      // Execute the query to insert the plan into the database
      connection.query(insertQuery, [name, features], (err, results) => {
        connection.release(); // Release the connection

        if (err) {
          console.error("Error inserting jobseeker plan into database:", err);
          return res
            .status(500)
            .json({ message: "Failed to create jobseeker plan." });
        }

        // Prepare the response data
        const newPlanId = results.insertId;

        res.status(201).json({
          message: "Jobseeker plan created successfully.",
          data: {
            id: newPlanId,
            name: name,
            features: features,
          },
        });
      });
    });
  } catch (error) {
    console.error("Error creating jobseeker plan:", error);
    res.status(500).json({ message: "Failed to create jobseeker plan." });
  }
};

// Fetch jobseeker plan by candidate ID
exports.getJobSeekerPlanByCandidateId = async (req, res) => {
  const { cand_id } = req.params;

  try {
    // Get a connection from the pool
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res.status(500).send("Failed to connect to the database.");
      }

      // SQL query to fetch jobseeker plan by candidate ID
      const sqlQuery = `
              SELECT plan_id
              FROM jobseekerplans
              WHERE cand_id = ?;
          `;

      // Execute the query
      connection.query(sqlQuery, [cand_id], (err, results) => {
        connection.release(); // Release the connection

        if (err) {
          console.error("Error fetching jobseeker plan from database:", err);
          return res.status(500).send("Failed to fetch jobseeker plan.");
        }

        if (results.length === 0) {
          return res
            .status(404)
            .json({ message: "Job seeker plan not found." });
        }

        // Send the response
        res.status(200).json({
          message: "Job seeker plan fetched successfully.",
          data: results[0], // Assuming only one plan is associated with a candidate
        });
      });
    });
  } catch (error) {
    console.error("Error fetching job seeker plan:", error);
    res.status(500).send("Failed to fetch job seeker plan.");
  }
};
