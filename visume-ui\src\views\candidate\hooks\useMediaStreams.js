import { useState, useCallback } from "react";
import toast from "react-hot-toast";

export function useMediaStreams() {
  const [localCamStream, setLocalCamStream] = useState(null);


  const startWebcam = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
        },
      });

      // Check if the stream contains both video and audio tracks
      const videoTrack = stream.getVideoTracks()[0];
      const audioTrack = stream.getAudioTracks()[0];

      if (!videoTrack || !audioTrack) {
        stream.getTracks().forEach((track) => track.stop());
        return toast.error("Both webcam and microphone must be shared to start the interview.");
      }
      setLocalCamStream(stream);
    } catch (err) {
      console.error("Error starting webcam and audio: ", err);
      toast.error("There was an error accessing your camera or microphone.");

    }
  }, []);

  const stopAllStreams = useCallback(() => {
    console.log("stopAllStreams called");

    if (localCamStream) {
      const tracks = localCamStream.getTracks();
      console.log(`Stopping ${tracks.length} media tracks from main camera stream`);

      tracks.forEach((track) => {
        console.log(`Stopping ${track.kind} track (${track.label})`);
        track.stop();
      });

      setLocalCamStream(null);
      console.log("Main camera stream cleaned up successfully");
    } else {
      console.log("No main camera stream to stop");
    }
  }, [localCamStream]);

  return {
    localCamStream,
    startWebcam,
    stopAllStreams,
  };
}
