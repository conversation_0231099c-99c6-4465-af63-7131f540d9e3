import React, { useState } from "react";
import toast from "react-hot-toast";
import {
  <PERSON>,
  Eye,
  Clipboard<PERSON>he<PERSON>,
  Users,
  Trash2,
  ArrowRight,
  VideoIcon,
  Star,
  Clock,
  TrendingUp
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { HiArrowRight } from "react-icons/hi";

const VideoProfileCard = ({ profile, toggleVideoProfilePopup }) => {
  const [loader, setLoader] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [resumeIdToDelete, setResumeIdToDelete] = useState(null);

  const deleteVisume = async (id) => {
    try {
      setLoader(true);
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/video-resume/${id}`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        toast.success("Video profile deleted successfully");
        window.location.reload(); // Ensure the page is reloaded to reflect changes
      } else {
        console.error("Failed to delete video profile");
      }
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoader(false);
      setIsModalOpen(false); // Close modal after delete attempt
    }
  };

  const handleDeleteClick = (id) => {
    setResumeIdToDelete(id);
    setIsModalOpen(true);
  };

  const navigate = useNavigate();

  return (
    <div className="group relative bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-lg p-3 hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-md transition-all duration-200">
      {/* Ultra Compact Single Row Layout */}
      <div className="flex items-center justify-between gap-3">
        {/* Left: Icon + Role + Skills */}
        <div className="flex items-center gap-3 flex-1 min-w-0">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <VideoIcon className="w-4 h-4 text-white" />
            </div>
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="text-sm font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors truncate">
                {profile.role}
              </h3>
              {/* Status Badge Inline */}
              {(profile.status?.toLowerCase() === "notsubmitted" ||
               profile.status?.toLowerCase() === "started" ||
               !profile.status ||
               profile.status.trim() === "") ? (
                <span className="inline-flex items-center gap-1 px-1.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-700 dark:bg-yellow-900/50 dark:text-yellow-300">
                  <Clock className="w-2.5 h-2.5" />
                  Draft
                </span>
              ) : (
                <span className="inline-flex items-center gap-1 px-1.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300">
                  <Star className="w-2.5 h-2.5" />
                  Live
                </span>
              )}
            </div>
            
            {/* Skills and Stats in one line */}
            <div className="flex items-center gap-3 text-xs">
              <div className="flex flex-wrap gap-1">
                {profile.skills.slice(0, 2).map((skill, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400"
                  >
                    {skill}
                  </span>
                ))}
                {profile.skills.length > 2 && (
                  <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-200 text-gray-500 dark:bg-gray-700 dark:text-gray-500">
                    +{profile.skills.length - 2}
                  </span>
                )}
              </div>
              
              {/* Inline Stats */}
              <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
                <div className="flex items-center gap-1">
                  <Eye className="w-3 h-3 text-blue-500" />
                  <span className="font-medium text-gray-900 dark:text-white">10</span>
                </div>
                <div className="flex items-center gap-1">
                  <ClipboardCheck className="w-3 h-3 text-green-500" />
                  <span className="font-medium text-gray-900 dark:text-white">3</span>
                </div>
                <div className="flex items-center gap-1">
                  <Users className="w-3 h-3 text-purple-500" />
                  <span className="font-medium text-gray-900 dark:text-white">1</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right: Action Buttons */}
        <div className="flex items-center gap-2 flex-shrink-0">
          {(profile.status?.toLowerCase() === "notsubmitted" ||
           profile.status?.toLowerCase() === "started" ||
           !profile.status ||
           profile.status.trim() === "") ? (
            <>
              <button
                onClick={() => navigate(`/candidate/interview/${profile.vpid}`)}
                className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white text-sm font-semibold rounded-md transition-all duration-200"
              >
                <ArrowRight className="w-4 h-4" />
                Continue
              </button>
              <button
                onClick={() => handleDeleteClick(profile.vpid)}
                className="inline-flex items-center gap-1 px-1.5 py-1 text-red-600 hover:text-red-700 text-xs font-medium transition-colors"
              >
                {loader ? (
                  <svg
                    className="h-3 w-3 animate-spin"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                ) : (
                  <Trash2 className="w-3 h-3" />
                )}
              </button>
            </>
          ) : (
            <button
              onClick={() => navigate(`/candidate/videoResume/${profile.vpid}`)}
              className="inline-flex items-center gap-1 px-2.5 py-1 border border-blue-500 text-blue-600 dark:text-blue-400 hover:bg-blue-500 hover:text-white text-xs font-medium rounded transition-all duration-200"
            >
              <Play className="w-3 h-3" />
              View
            </button>
          )}
        </div>
      </div>

      {/* Enhanced Confirmation Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-50">
          <div className="bg-white dark:bg-gray-900 rounded-xl shadow-2xl max-w-md w-full mx-4 overflow-hidden">
            {/* Modal Header */}
            <div className="p-6 border-b border-gray-200 dark:border-gray-800">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-red-100 dark:bg-red-900/50 rounded-lg">
                  <Trash2 className="w-5 h-5 text-red-600 dark:text-red-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Delete Video Resume
                </h3>
              </div>
            </div>
            
            {/* Modal Content */}
            <div className="p-6">
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                Are you sure you want to delete this video resume? This action cannot be undone and all associated data will be permanently removed.
              </p>
              
              {/* Action Buttons */}
              <div className="flex items-center justify-end gap-3">
                <button
                  onClick={() => setIsModalOpen(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => deleteVisume(resumeIdToDelete)}
                  disabled={loader}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-colors flex items-center gap-2"
                >
                  {loader ? (
                    <>
                      <svg
                        className="h-4 w-4 animate-spin text-white"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                          fill="none"
                        />
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        />
                      </svg>
                      Deleting...
                    </>
                  ) : (
                    <>
                      <Trash2 className="w-4 h-4" />
                      Delete
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoProfileCard;