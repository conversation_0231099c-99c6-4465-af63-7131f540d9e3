/**
 * Test to verify media stream cleanup functionality
 * This test ensures that camera and microphone tracks are properly stopped
 * when the interview ends, turning off browser media usage indicators.
 */

// Mock navigator.mediaDevices.getUserMedia
const mockGetUserMedia = jest.fn();
const mockTrack = {
  kind: 'audio',
  label: 'Mock Audio Track',
  stop: jest.fn()
};

const mockStream = {
  getTracks: jest.fn(() => [mockTrack]),
  getAudioTracks: jest.fn(() => [mockTrack]),
  getVideoTracks: jest.fn(() => [mockTrack])
};

// Setup global mocks
global.navigator = {
  mediaDevices: {
    getUserMedia: mockGetUserMedia
  }
};

global.MediaRecorder = jest.fn().mockImplementation(() => ({
  start: jest.fn(),
  stop: jest.fn(),
  state: 'inactive',
  ondataavailable: null,
  onstop: null,
  onstart: null
}));

global.AudioContext = jest.fn().mockImplementation(() => ({
  createAnalyser: jest.fn(() => ({
    fftSize: 256,
    frequencyBinCount: 128,
    getByteFrequencyData: jest.fn()
  })),
  createMediaStreamSource: jest.fn(() => ({
    connect: jest.fn()
  })),
  close: jest.fn(),
  state: 'running'
}));

global.requestAnimationFrame = jest.fn(cb => setTimeout(cb, 16));

describe('Media Stream Cleanup Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockGetUserMedia.mockResolvedValue(mockStream);
  });

  test('should stop audio tracks when stopRecognition is called', async () => {
    // Import the component after mocks are set up
    const { render, fireEvent, waitFor } = require('@testing-library/react');
    const React = require('react');
    
    // Mock the InterviewSection component with our cleanup logic
    const TestComponent = () => {
      const [isListening, setIsListening] = React.useState(false);
      const audioStreamRef = React.useRef(null);
      const mediaRecorderRef = React.useRef(null);

      const stopRecognition = React.useCallback(async () => {
        if (!mediaRecorderRef.current || !isListening) return;
        try {
          mediaRecorderRef.current.stop();
          
          // Stop the audio stream tracks to turn off microphone indicator
          if (audioStreamRef.current) {
            audioStreamRef.current.getTracks().forEach(track => {
              track.stop();
              console.log("Stopped audio track:", track.kind);
            });
            audioStreamRef.current = null;
          }
          
          setIsListening(false);
          return true;
        } catch (error) {
          console.error("Error stopping recognition:", error);
          return false;
        }
      }, [isListening]);

      const startRecognition = React.useCallback(async () => {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        audioStreamRef.current = stream;
        
        const mediaRecorder = new MediaRecorder(stream);
        mediaRecorderRef.current = mediaRecorder;
        setIsListening(true);
      }, []);

      return (
        <div>
          <button onClick={startRecognition} data-testid="start-recording">
            Start Recording
          </button>
          <button onClick={stopRecognition} data-testid="stop-recording">
            Stop Recording
          </button>
          <div data-testid="listening-status">
            {isListening ? 'Listening' : 'Not Listening'}
          </div>
        </div>
      );
    };

    const { getByTestId } = render(<TestComponent />);

    // Start recording
    fireEvent.click(getByTestId('start-recording'));
    
    await waitFor(() => {
      expect(getByTestId('listening-status')).toHaveTextContent('Listening');
    });

    // Verify getUserMedia was called
    expect(mockGetUserMedia).toHaveBeenCalledWith({ audio: true });

    // Stop recording
    fireEvent.click(getByTestId('stop-recording'));

    await waitFor(() => {
      expect(getByTestId('listening-status')).toHaveTextContent('Not Listening');
    });

    // Verify that track.stop() was called
    expect(mockTrack.stop).toHaveBeenCalled();
  });

  test('should clean up device testing audio stream on unmount', () => {
    const { render, unmount } = require('@testing-library/react');
    const React = require('react');

    const DeviceTestComponent = () => {
      const [audioStream, setAudioStream] = React.useState(null);
      const [audioContext, setAudioContext] = React.useState(null);

      React.useEffect(() => {
        const startAudioStream = async () => {
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
          setAudioStream(stream);
          
          const newAudioContext = new AudioContext();
          setAudioContext(newAudioContext);
        };

        startAudioStream();

        return () => {
          if (audioStream) {
            audioStream.getTracks().forEach(track => track.stop());
          }
          if (audioContext && audioContext.state !== 'closed') {
            audioContext.close();
          }
        };
      }, []);

      return <div data-testid="device-test">Device Testing</div>;
    };

    const { getByTestId } = render(<DeviceTestComponent />);
    expect(getByTestId('device-test')).toBeInTheDocument();

    // Unmount the component
    unmount();

    // Verify cleanup was called
    expect(mockTrack.stop).toHaveBeenCalled();
  });

  test('should stop main camera stream when stopAllStreams is called', () => {
    const { render } = require('@testing-library/react');
    const React = require('react');

    const MainStreamComponent = () => {
      const [localCamStream, setLocalCamStream] = React.useState(mockStream);

      const stopAllStreams = React.useCallback(() => {
        if (localCamStream) {
          const tracks = localCamStream.getTracks();
          tracks.forEach((track) => {
            track.stop();
          });
          setLocalCamStream(null);
        }
      }, [localCamStream]);

      React.useEffect(() => {
        // Simulate stopping streams when component unmounts
        return () => {
          stopAllStreams();
        };
      }, [stopAllStreams]);

      return <div data-testid="main-stream">Main Stream</div>;
    };

    const { getByTestId, unmount } = render(<MainStreamComponent />);
    expect(getByTestId('main-stream')).toBeInTheDocument();

    // Unmount to trigger cleanup
    unmount();

    // Verify that track.stop() was called
    expect(mockTrack.stop).toHaveBeenCalled();
  });
});
