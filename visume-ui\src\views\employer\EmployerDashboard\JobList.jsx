import React, { useState, useEffect } from "react";
import PositionsCard from "../components/PositionsCard";
import ProfileCard from "../ProfilesUI/ProfileCard";
import { HiChevronDown, HiChevronUp } from "react-icons/hi";

const JobList = ({ jobData, ProfileSkelLoader }) => {
  const [expandedJobs, setExpandedJobs] = useState({});
  const [matchedProfiles, setMatchedProfiles] = useState({});
  const [loadingProfiles, setLoadingProfiles] = useState({});
  const [errors, setErrors] = useState({});

  const filterMatchingProfiles = (profiles) => {
    if (!profiles || profiles.length === 0) return [];
    return profiles.filter(profile => {
      const score = Math.round(JSON.parse(profile.score)?.score?.Overall_Score) || 0;
      return score >= 50;
    });
  };

  useEffect(() => {
    if (jobData && jobData.length > 0) {
      const matchedProfiles = filterMatchingProfiles(jobData);
      setMatchedProfiles(prev => ({
        ...prev,
        [jobData[0].id]: matchedProfiles
      }));
    }
  }, [jobData]);

  const toggleJobExpansion = (jobId) => {
    setExpandedJobs(prev => ({
      ...prev,
      [jobId]: !prev[jobId]
    }));
  };

  return (
    <div className="col-span-full rounded-2xl bg-white p-6 shadow-lg dark:bg-navy-700 dark:text-white">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="flex flex-row items-center text-lg font-semibold text-gray-800 dark:text-white">
          <span className="mr-2">Active Job Listings</span>
        </h2>
      </div>
      {jobData && jobData.length > 0 && (
        <div className="flex flex-col space-y-4">
          {jobData.map((job) =>
            job.id ? (
              <div key={job.id} className="rounded-lg border border-gray-200">
                <div className="relative">
                  <PositionsCard job={job} />
                  <button
                    onClick={() => toggleJobExpansion(job.id)}
                    className="absolute right-4 top-1/2 -translate-y-1/2 transform rounded-full bg-gray-100 p-2 hover:bg-gray-200 dark:bg-navy-600 dark:hover:bg-navy-500"
                  >
                    {expandedJobs[job.id] ? (
                      <HiChevronUp className="h-5 w-5" />
                    ) : (
                      <HiChevronDown className="h-5 w-5" />
                    )}
                  </button>
                </div>

                {expandedJobs[job.id] && (
                  <div className="border-t border-gray-200 p-4">
                    <h3 className="mb-4 text-md font-semibold">Matching Profiles</h3>
                    {loadingProfiles[job.id] ? (
                      <div className="grid gap-4 md:grid-cols-2">
                        {[1, 2].map((i) => (
                          <ProfileSkelLoader key={i} />
                        ))}
                      </div>
                    ) : errors[job.id] ? (
                      <div className="text-center text-red-500">{errors[job.id]}</div>
                    ) : matchedProfiles[job.id]?.length > 0 ? (
                      <div className="grid gap-4 md:grid-cols-1">
                        {matchedProfiles[job.id]?.map((profile) => (
                          <div key={profile.id} className="bg-gray-50 rounded-lg p-4">
                            <div className="mb-2">
                              <h4 className="text-md font-semibold">{profile.candidateDetails[0].cand_name}</h4>
                              <div className="text-sm text-gray-600">
                                Experience: {profile.experience_range} years
                              </div>
                            </div>
                            <div className="flex flex-wrap gap-2 mb-3">
                              {profile.skills?.split(',').map((skill, idx) => (
                                <span key={idx} className="px-2 py-1 bg-gray-200 rounded-full text-xs">
                                  {skill.trim()}
                                </span>
                              ))}
                            </div>
                            <a
                              href={`/profile/${profile.video_profile_id}`}
                              className="text-brand-500 hover:text-brand-600 text-sm font-medium inline-flex items-center"
                            >
                              View Profile →
                            </a>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center text-gray-500">
                        No matching profiles found
                      </div>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <ProfileSkelLoader key={`skeleton-${Math.random()}`} />
            )
          )}
        </div>
      )}
    </div>
  );
};

export default JobList;