# [Horizon UI TailwindCSS React ⚡️](https://horizon-ui.com/horizon-tailwind-react) [![Tweet](https://img.shields.io/twitter/url/http/shields.io.svg?style=social&logo=twitter)](https://twitter.com/intent/tweet?text=Check%20Horizon%20UI,%20the%20trendiest%20open-source%20admin%20template%20for%20%23tailwindcss%20and%20%23react!%0A%0Ahorizon-ui.com%20)

![version](https://img.shields.io/badge/version-1.1.0-brightgreen.svg)
![license](https://img.shields.io/badge/license-MIT-blue.svg)
[![GitHub issues open](https://img.shields.io/github/issues/horizon-ui/horizon-tailwind-react.svg?maxAge=2592000)](https://github.com/horizon-ui/horizon-tailwind-react/issues?q=is%3Aopen+is%3Aissue)

<p>&nbsp;</p>

[<img alt="Horizon UI - Tailwind CSS React Admin dashboard template" src="https://i.ibb.co/1zhBQ2J/horizon-ui-tailwind-2.png" />](https://github.com/horizon-ui/horizon-tailwind-react)

<p>&nbsp;</p>


Get started and build your dream web app with Horizon UI, the trendiest & innovative Open Source Admin Template for Tailwind CSS & React!

---

### Introduction

Designed for those who like modern UI elements and beautiful websites. Made of hundred of elements, designed blocks and fully coded pages, Horizon UI is ready to help you create stunning websites and webapps.

Save hundreds of hours trying to create and develop a dashboard from scratch.
The fastest, most responsive & trendiest dashboard for Tailwind CSS is here. Seriously.

With Horizon UI you will find many examples for pages like NFTs Pages,
Authentication Pages, Profile and so on. Just choose between a Basic Design or a cover and you are good to go!

### 🎉 [NEW] Horizon UI Components
All the main components from both versions, this will help you to see and interact with all & the latest added components of Horizon (also, new components are on the way, stay tuned)! ⚡️
<a href="https://horizon-ui.com/components/?ref=readme-horizon-tailwind-react" target="_blank">See all components</a>


### Documentation

Each element is well presented in a very complex documentation. You can read more about the <a href="https://horizon-ui.com/docs-tailwind/docs/react/installation?ref=readme-horizon-tailwind-react" target="_blank">documentation here.</a>

### Quick Start

Install Horizon UI by running either of the following:

- Install NodeJS LTS from [NodeJs Official Page](https://nodejs.org/en/?ref=horizon-documentation) (NOTE: Product only works with LTS version).

Clone the repository with the following command:

```bash
git clone https://github.com/horizon-ui/horizon-tailwind-react.git
```

Run in terminal this command:

```bash
npm install
```

Then run this command to start your local server

```bash
npm start
```

### Example Pages

If you want to get inspiration or just show something directly to your clients, you can jump start your development with our pre-built example pages. You will be able to quickly set up the basic structure for your web project.

View <a href="https://horizon-ui.com/horizon-tailwind-react/?ref=readme-horizon-tailwind-react" target="_blank">example pages here.</a>

### Versions

| Free Version                                                                                                       | PRO Version                                                                                                               |
| ------------------------------------------------------------------------------------------------------------------ | ------------------------------------------------------------------------------------------------------------------------- |
| [![Horizon UI Tailwind CSS React](https://i.ibb.co/1zhBQ2J/horizon-ui-tailwind-2.png)](https://www.horizon-ui.com/?ref=readme-horizon-tailwind-react) | [![Horizon UI Tailwind CSS React PRO](https://i.ibb.co/d0cVzKB/horizon-ui-pro-tailwind.png)](https://www.horizon-ui.com/pro?ref=readme-horizon-tailwind-react) |

### Figma Version

Horizon UI is available in Figma format as well! Check it out on Figma
Community! 🎨
[See the Horizon UI Figma design files](https://bit.ly/horizon-figma)

### Reporting Issues

We use GitHub Issues as the official bug tracker for the Horizon UI. Here are
some advices for our users that want to report an issue:

1. Make sure that you are using the latest version of the Horizon UI Dashbaord.
   Check the CHANGELOG from your dashboard on our
   [CHANGE LOG File](https://github.com/horizon-ui/horizon-tailwind-react/blob/main/CHANGELOG.md?ref=readme-horizon-tailwind-react).
2. Providing us reproducible steps for the issue will shorten the time it takes
   for it to be fixed.
3. Some issues may be browser specific, so specifying in what browser you
   encountered the issue might help.

---

### Community

Connect with the community! Feel free to ask questions, report issues, and meet new people that already use Horizon UI!

💬 [Join the #HorizonUI Discord Community!](https://discord.gg/f6tEKFBd4m)

### Copyright and license

⭐️ [Copyright 2023 Horizon UI ](https://www.horizon-ui.com/?ref=readme-horizon-tailwind-react)

📄 [Horizon UI License](https://www.simmmple.com/licenses?ref=readme-horizon-tailwind-react)
