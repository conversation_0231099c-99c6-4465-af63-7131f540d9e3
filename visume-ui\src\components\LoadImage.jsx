import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

const LoadImage = ({ 
  src, 
  alt, 
  fallbackSrc, 
  timeout = 10000,
  className,
  onLoad,
  onError,
  ...props 
}) => {
  const [imgSrc, setImgSrc] = useState(src);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const controller = new AbortController();
    const signal = controller.signal;

    const loadImage = async () => {
      try {
        setLoading(true);
        setError(null);

        // Create timeout promise
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Image load timeout')), timeout);
        });

        // Create image load promise
        const loadPromise = new Promise((resolve, reject) => {
          const img = new Image();
          img.src = src;
          img.onload = () => resolve(src);
          img.onerror = () => reject(new Error('Failed to load image'));
        });

        // Race between timeout and image load
        const loadedSrc = await Promise.race([loadPromise, timeoutPromise]);
        setImgSrc(loadedSrc);
        setLoading(false);
        onLoad?.();
      } catch (err) {
        console.error('Image load error:', err);
        setError(err);
        setImgSrc(fallbackSrc);
        onError?.(err);
      }
    };

    loadImage();

    return () => {
      controller.abort();
    };
  }, [src, fallbackSrc, timeout, onLoad, onError]);

  if (loading) {
    return <div className="animate-pulse bg-gray-200 h-full w-full" />;
  }

  if (error && !fallbackSrc) {
    return (
      <div className="flex items-center justify-center h-full w-full bg-gray-100 text-gray-500">
        Failed to load image
      </div>
    );
  }

  return (
    <img
      src={imgSrc}
      alt={alt}
      className={className}
      {...props}
    />
  );
};

LoadImage.propTypes = {
  src: PropTypes.string.isRequired,
  alt: PropTypes.string.isRequired,
  fallbackSrc: PropTypes.string,
  timeout: PropTypes.number,
  className: PropTypes.string,
  onLoad: PropTypes.func,
  onError: PropTypes.func
};

export default LoadImage;