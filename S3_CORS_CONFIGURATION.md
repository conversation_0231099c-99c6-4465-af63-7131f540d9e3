# S3 Bucket CORS Configuration for Multipart Uploads

## Overview

This document outlines the required CORS configuration for your S3 bucket to support multipart video uploads with ETag header access.

## Problem

The multipart upload process was failing with the error:
```
Part X upload succeeded but no ETag received
```

This occurs because:
1. S3 returns ETag headers in responses to pre-signed URL uploads
2. Browser CORS policy blocks access to response headers not explicitly exposed
3. The frontend JavaScript cannot access the ETag header needed for multipart upload completion

## Solution

### 1. Backend CORS Configuration (✅ WORKING)

Updated `visume-api/app.js` to expose ETag headers:

```javascript
app.use(cors({
  // ... other options
  exposedHeaders: ['ETag', 'Content-Length', 'Content-Type']
}));
```

### 2. S3 Bucket CORS Configuration (REQUIRED)

Our S3 bucket must have the following CORS configuration:

```json
[
  {
    "AllowedHeaders": [
      "*"
    ],
    "AllowedMethods": [
      "GET",
      "PUT",
      "POST",
      "DELETE",
      "HEAD"
    ],
    "AllowedOrigins": [
      "http://localhost:3000",
      "http://localhost:5173",
      "https://visume.co.in"
    ],
    "ExposeHeaders": [
      "ETag",
      "Content-Length",
      "Content-Type",
      "x-amz-request-id",
      "x-amz-id-2"
    ],
    "MaxAgeSeconds": 3000
  }
]
```
## AI GENERATED BELOW

### 3. How to Apply S3 CORS Configuration

#### Option A: AWS Console
1. Go to AWS S3 Console
2. Select your bucket
3. Go to "Permissions" tab
4. Scroll down to "Cross-origin resource sharing (CORS)"
5. Click "Edit"
6. Paste the JSON configuration above
7. Click "Save changes"

#### Option B: AWS CLI
```bash
aws s3api put-bucket-cors --bucket YOUR_BUCKET_NAME --cors-configuration file://cors-config.json
```

#### Option C: AWS SDK (Node.js)
```javascript
const { S3Client, PutBucketCorsCommand } = require("@aws-sdk/client-s3");

const corsConfiguration = {
  CORSRules: [
    {
      AllowedHeaders: ["*"],
      AllowedMethods: ["GET", "PUT", "POST", "DELETE", "HEAD"],
      AllowedOrigins: [
        "http://localhost:3000",
        "http://localhost:5173", 
        "https://visume.co.in"
      ],
      ExposeHeaders: [
        "ETag",
        "Content-Length", 
        "Content-Type",
        "x-amz-request-id",
        "x-amz-id-2"
      ],
      MaxAgeSeconds: 3000
    }
  ]
};

const command = new PutBucketCorsCommand({
  Bucket: process.env.AWS_BUCKET_NAME,
  CORSConfiguration: corsConfiguration
});

await s3Client.send(command);
```

## Key Points

1. **ExposeHeaders is Critical**: The `ETag` header must be in the `ExposeHeaders` array for the frontend to access it.

2. **AllowedOrigins**: Update the origins list to match your actual frontend domains.

3. **AllowedMethods**: Include `PUT` for multipart uploads and `POST` for initialization/completion.

4. **MaxAgeSeconds**: Controls how long browsers cache the CORS preflight response.

## Verification

After applying the CORS configuration, you can verify it works by:

1. Checking browser developer tools for CORS errors
2. Looking for ETag headers in network responses
3. Testing multipart upload functionality

## Troubleshooting

If you still see ETag issues after applying this configuration:

1. **Clear browser cache** - CORS responses are cached
2. **Wait a few minutes** - S3 CORS changes can take time to propagate
3. **Check bucket policy** - Ensure your bucket policy allows the necessary operations
4. **Verify origins** - Make sure your frontend domain is in the AllowedOrigins list

## Related Files

- `visume-api/app.js` - Backend CORS configuration
- `visume-ui/src/utils/multipartUpload.js` - Frontend multipart upload implementation
- `visume-api/controllers/s3UploadController.js` - S3 upload controller
