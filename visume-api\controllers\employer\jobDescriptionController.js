// Job Description Handling Functions

const pool = require("../../config/db");
const fs = require("fs");
const { jobDescStripper } = require("../../utils/helpers");
const { helperFilterCandidates } = require("../videoProfileController");

exports.uploadJobDescription = async (req, res) => {
  const { emp_id } = req.params;

  const jobDesc = req.files?.job_description
    ? req.files.job_description[0]
    : null;

  if (!jobDesc) {
    return res.status(400).json({ message: "Job description is required." });
  }

  if (!emp_id) {
    return res.status(400).json({ message: "Employer ID is required." });
  }
  try {
    const conn = await pool.promise().getConnection();

    // Check if Employer ID is valid
    const checkEmployerQuery = "SELECT id FROM employer WHERE id = ?";
    const [employer] = await conn.query(checkEmployerQuery, [emp_id]);

    if (employer.length === 0) {
      return res.status(404).json({ message: "Invalid Employer ID." });
    }

    // Load the uploaded PDF file
    const pdfBuffer = fs.readFileSync(jobDesc.path);

    // Process the PDF and fetch AI-enhanced data
    console.log("Fetching data from AI...");
    const enhancedData = await jobDescStripper(pdfBuffer);
    console.log("Data Fetched");

    // Send the processed response
    res.status(200).json({
      JobDescription: enhancedData,
      filePath: jobDesc.path,
      message: "Job Description Uploaded Successfully.",
    });
  } catch (error) {
    console.error("Error processing job description:", error);
    return res
      .status(500)
      .json({ message: "Failed to process job description." });
  }
};

exports.createJobDescription = async (req, res) => {
  const { emp_id, role, skills, experience, location, filePath } = req.body;

  if (!emp_id) {
    return res.status(400).json({ message: "Employer ID is required." });
  }

  try {
    const conn = await pool.promise().getConnection();

    // Check if Employer ID is valid
    const checkEmployerQuery = "SELECT id FROM employer WHERE id = ?";
    const [employer] = await conn.query(checkEmployerQuery, [emp_id]);

    if (employer.length === 0) {
      return res.status(404).json({ message: "Invalid Employer ID." });
    }

    // Call helper function to filter candidates
    const candidates = await helperFilterCandidates({
      preferred_location: location,
      role,
      selectedSkills: skills,
      experience,
    });

    // Extract video profile IDs from the filtered candidates
    const suggested_profiles = candidates.map((e) => e.video_profile_id);

    // Create job description object as JS object (not stringified)
    const JobDescription = {
      role,
      skills,
      experience,
      location,
      filePath,
    };

    const createQuery = `
      INSERT INTO job_descriptions (employer_id, suggested_profiles, JobDescription)
      VALUES (?, ?, ?)
    `;

    await conn.query(createQuery, [
      emp_id,
      JSON.stringify(suggested_profiles),
      JSON.stringify(JobDescription),
    ]);

    res.json({
      message: "Job Description created successfully."
    });
  } catch (error) {
    console.error("Error creating Job Description:", error);
    res
      .status(500)
      .json({ message: "Failed to create Job Description.", error });
  }
};
/**
 * Get the latest job description for an employer
 */
exports.getJobDescriptionByEmployer = async (req, res) => {
  const { emp_id } = req.params;
  if (!emp_id) {
    return res.status(400).json({ message: "Employer ID is required." });
  }
  try {
    const conn = await pool.promise().getConnection();
    // Get the latest job description for this employer
    const [rows] = await conn.query(
      `SELECT id, JobDescription FROM job_descriptions WHERE employer_id = ? ORDER BY id DESC LIMIT 1`,
      [emp_id]
    );
    if (!rows.length) {
      return res.status(404).json({ message: "No job description found for this employer." });
    }
    const jd = rows[0];
    let parsedJD = {};
    if (typeof jd.JobDescription === "string") {
      try {
        parsedJD = JSON.parse(jd.JobDescription);
      } catch {
        parsedJD = {};
      }
    } else if (typeof jd.JobDescription === "object" && jd.JobDescription !== null) {
      parsedJD = jd.JobDescription;
    }
    res.json({
      jobDescription: {
        _id: jd.id,
        ...parsedJD,
      },
    });
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch job description.", error });
  }
};

/**
 * Delete a job description by ID
 */
exports.deleteJobDescriptionById = async (req, res) => {
  const { id } = req.params;
  if (!id) {
    return res.status(400).json({ message: "Job Description ID is required." });
  }
  try {
    const conn = await pool.promise().getConnection();
    const [result] = await conn.query(
      "DELETE FROM job_descriptions WHERE id = ?",
      [id]
    );
    if (result.affectedRows === 0) {
      return res.status(404).json({ message: "Job description not found." });
    }
    res.json({ message: "Job description deleted successfully." });
  } catch (error) {
    res.status(500).json({ message: "Failed to delete job description.", error });
  }
};