const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const csv = require('csv-parse/sync');

const prisma = new PrismaClient();

async function main() {
  try {
    // Read and parse roles CSV
    const rolesContent = fs.readFileSync('./utils/files/roles.csv', 'utf-8');
    const roles = csv.parse(rolesContent, { columns: true });
    
    // Import roles
    for (const role of roles) {
      await prisma.roles.create({
        data: {
          role_name: role.role_name
        }
      });
    }
    console.log('Roles imported successfully');

    // Read and parse skills CSV
    const skillsContent = fs.readFileSync('./utils/files/skills.csv', 'utf-8');
    const skills = csv.parse(skillsContent, { columns: true });
    
    // Import skills
    for (const skill of skills) {
      await prisma.skills.create({
        data: {
          skill_name: skill.skill_name
        }
      });
    }
    console.log('Skills imported successfully');

  } catch (error) {
    console.error('Error seeding data:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();