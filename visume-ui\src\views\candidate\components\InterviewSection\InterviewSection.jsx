// InterviewSection.jsx
import React, { useEffect, useState, useRef, useCallback } from "react";
import toast from "react-hot-toast";
import { PollyClient, SynthesizeSpeechCommand } from "@aws-sdk/client-polly";
import { fromCognitoIdentityPool } from "@aws-sdk/credential-provider-cognito-identity";
import InterviewSectionContent from "./InterviewSectionContent";
import api from "../../../../api/axios.js";

// Configure Polly Client using Cognito Identity Pool credentials
const pollyClient = new PollyClient({
  region: import.meta.env.VITE_AWS_REGION,
  credentials: fromCognitoIdentityPool({
    clientConfig: { region: import.meta.env.VITE_AWS_REGION },
    identityPoolId: import.meta.env.VITE_AWS_POOL_ID,
  }),
});

export default function InterviewSection({
  localCamStream,
  questions,
  currentQuestion,
  currentIndex,
  onNextQuestion,
  onEndInterview,
  error,
  isLoading,
  startAnswering,
  updateAnswer,
  isInterviewActive = false,
  isTransitioning,
}) {
  // State
  const videoRef = useRef(null); // Ref for video element

  // Rehydrate Q&A history from localStorage if available
  const [questionsState, setQuestionsState] = useState(() => {
    const saved = localStorage.getItem("questions");
    return saved ? JSON.parse(saved) : questions;
  });

  useEffect(() => {
    const saved = localStorage.getItem("questions");
    if (saved) {
      setQuestionsState(JSON.parse(saved));
    }
  }, []);

  // Sync questionsState with prop changes
  useEffect(() => {
    if (questions && questions.length > 0) {
      setQuestionsState(questions);
    }
  }, [questions]);

  const [remainingTime, setRemainingTime] = useState(
    currentQuestion?.timerDuration || 90
  );
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [hasNarrated, setHasNarrated] = useState(false);

  // Prevent double submission of last answer
  const lastAnswerSavedRef = useRef(false);

  // Warn on browser back navigation during interview
  useEffect(() => {
    window.history.pushState(null, "", window.location.href);

    let isBackNav = false;

    const handlePopState = (event) => {
      if (isInterviewActive) {
        isBackNav = true;
        event.preventDefault?.();
        const confirmed = window.confirm(
          "Are you sure you want to leave? Your progress will be lost."
        );
        if (confirmed) {
          window.removeEventListener("beforeunload", handleBeforeUnload);
          window.location.href = "/candidate/dashboard";
        } else {
          window.history.pushState(null, "", window.location.href);
          isBackNav = false;
        }
      }
    };

    const handleBeforeUnload = (event) => {
      if (isInterviewActive && !isBackNav) {
        event.preventDefault();
        event.returnValue =
          "Data will be lost on reload. Do you want to continue?";
        return "Data will be lost on reload. Do you want to continue?";
      }
      // If isBackNav, do not show reload warning
    };

    window.addEventListener("popstate", handlePopState);
    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
      window.removeEventListener("popstate", handlePopState);
    };
  }, [isInterviewActive]);

  // Handlers
  const stopRecognition = useCallback(async () => {
    if (!mediaRecorderRef.current || !isListening) return;
    try {
      mediaRecorderRef.current.stop();

      // Stop the audio stream tracks to turn off microphone indicator
      if (audioStreamRef.current) {
        audioStreamRef.current.getTracks().forEach(track => {
          track.stop();
          console.log("Stopped audio track:", track.kind);
        });
        audioStreamRef.current = null;
      }

      setIsListening(false);
      await new Promise((resolve) => setTimeout(resolve, 200));
      return true;
    } catch (error) {
      console.error("Error stopping recognition:", error);
      return false;
    }
  }, [isListening]);

  // Initialize refs
  const timerRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const audioStreamRef = useRef(null); // Track audio stream for cleanup
  const audioChunks = useRef([]);
  const audioElement = useRef(null);
  const timerExpirationInProgress = useRef(false);

  // Handle audio transcription and answers
  const transcribeAudio = useCallback(
    async (audioBlob) => {
      if (!audioBlob || audioBlob.size === 0) {
        return false;
      }
      setIsProcessing(true);
      let transcriptionSuccess = false;
      try {
        if (audioBlob.size === 0) {
          throw new Error("Empty audio recording");
        }
        const file = new File([audioBlob], "answer.webm", {
          type: "audio/webm;codecs=opus",
        });
        if (file.size === 0) {
          throw new Error("Failed to create audio file");
        }
        const formData = new FormData();
        formData.append("file", file);
        let response;
        try {
          response = await api.post("/api/v1/transcribe", formData);
          if (response.status !== 200) {
            throw new Error(`Transcription failed: ${response.status}`);
          }
        } catch (error) {
          throw new Error("Failed to reach transcription service");
        }
        const responseData = response.data;
        if (!responseData) {
          throw new Error(
            `Transcription failed: ${JSON.stringify(responseData)}`
          );
        }
        if (!responseData.text) {
          throw new Error("No transcription text in response");
        }
        const transcribedText = responseData.text?.trim();
        if (transcribedText) {
          try {
            // Prevent double submission for last question
            const isLastQuestion = currentIndex === questions.length - 1;
            if (isLastQuestion && lastAnswerSavedRef.current) {
              transcriptionSuccess = true;
            } else {
              await updateAnswer(transcribedText);
              if (isLastQuestion) lastAnswerSavedRef.current = true;
              transcriptionSuccess = true;
            }
          } catch (error) {
            toast.error("Failed to save answer. Please try again", {
              id: "transcription-toast",
              duration: 4000,
            });
          }
        } else {
          toast.error(
            "No speech detected - please speak clearly and try again",
            {
              id: "transcription-toast",
              duration: 4000,
            }
          );
        }
        transcriptionSuccess = true;
      } catch (error) {
        let errorMessage =
          "Could not transcribe audio - please type your answer";
        if (error.message.includes("Unsupported audio format")) {
          errorMessage = "Audio format not supported - please try again";
        } else if (error.message.includes("No transcription text")) {
          errorMessage =
            "No speech detected - please speak clearly and try again";
        } else if (error.message.includes("Invalid response")) {
          errorMessage = "Server error - please try again";
        }
        toast.error(errorMessage, {
          id: "transcription-toast",
          duration: 4000,
        });
      } finally {
        setIsProcessing(false);
      }
      return transcriptionSuccess;
    },
    [questions, currentIndex, updateAnswer]
  );

  // Effect to handle question narration
  useEffect(() => {
    if (!isInterviewActive) return;
    if (isLoading || isTransitioning) return;
    if (
      !currentQuestion ||
      currentIndex >= questions.length ||
      questions.length === 0
    )
      return;
    if (!currentQuestion?.question) return;
    if (isProcessing) return;
    if (isSpeaking) return;
    if (hasNarrated) return;
    if (audioElement.current) {
      audioElement.current.pause();
      audioElement.current.src = "";
    }
    let autoRecordTimeout;
    const speakQuestion = async () => {
      try {
        const questionFromArray =
          questions && questions[currentIndex]
            ? questions[currentIndex].question
            : null;
        const questionToSpeak =
          questionFromArray ||
          currentQuestion?.question ||
          "What is your experience with [skill]?";
        const params = {
          Text: questionToSpeak,
          OutputFormat: "mp3",
          VoiceId: "Raveena",
          SampleRate: "16000",
          TextType: "text",
        };
        const command = new SynthesizeSpeechCommand(params);
        const data = await pollyClient.send(command);
        if (data.AudioStream) {
          const audioBuffer = await new Response(
            data.AudioStream
          ).arrayBuffer();
          const audioBlob = new Blob([new Uint8Array(audioBuffer)], {
            type: "audio/mpeg",
          });
          const audio = new Audio(URL.createObjectURL(audioBlob));
          audioElement.current = audio;
          audio.onended = () => {
            setIsSpeaking(false);
            if (currentQuestion?.type?.toLowerCase() === "verbal") {
              autoRecordTimeout = setTimeout(() => {
                handleSpeechRecognition().catch(() => {
                  toast.error(
                    "Failed to auto-start recording. Please use the manual button."
                  );
                });
              }, 500);
            }
          };
          setIsSpeaking(true);
          await audio.play();
          setRemainingTime(currentQuestion?.timerDuration || 90);
          timerExpirationInProgress.current = false;
        }
      } catch (err) {
        setIsSpeaking(false);
      }
    };
    speakQuestion();
    setHasNarrated(true);
    return () => {
      if (audioElement.current) {
        audioElement.current.pause();
        audioElement.current.src = "";
      }
      if (autoRecordTimeout) {
        clearTimeout(autoRecordTimeout);
      }
      setIsSpeaking(false);
    };
  }, [
    currentQuestion?.question,
    currentIndex,
    hasNarrated,
    isSpeaking,
    isProcessing,
    isInterviewActive,
    isLoading,
    isTransitioning,
    questions,
  ]);

  useEffect(() => {
    if (currentQuestion?.question && currentIndex === 0 && hasNarrated) {
      setHasNarrated(false);
    }
  }, [currentQuestion?.question, currentIndex]);

  // Reset timer when question changes
  useEffect(() => {
    if (currentQuestion?.timerDuration) {
      setRemainingTime(currentQuestion.timerDuration);
    }
  }, [currentQuestion?.timerDuration, currentIndex]);

  const handleNextQuestion = useCallback(
    async (answerText = null) => {
      if (!isInterviewActive) return false; // Prevent running after interview ends
      if (isProcessing || isSpeaking) {
        return false;
      }
      try {
        setIsProcessing(true);
        if (answerText !== null) {
          try {
            // Update local questionsState with the answer
            setQuestionsState((prev) => {
              const updated = [...prev];
              if (updated[currentIndex]) {
                updated[currentIndex].answer = answerText;
              }
              localStorage.setItem("questions", JSON.stringify(updated));
              return updated;
            });
            await updateAnswer(answerText);
          } catch (error) {
            throw new Error("Failed to save typed answer");
          }
        } else {
          if (isListening && mediaRecorderRef.current) {
            mediaRecorderRef.current.ondataavailable = (event) => {
              if (event.data && event.data.size > 0) {
                audioChunks.current.push(event.data);
              }
            };
            mediaRecorderRef.current.stop();
            setIsListening(false);
            await new Promise((resolve) => {
              mediaRecorderRef.current.onstop = resolve;
            });
          }
          if (audioChunks.current.length > 0) {
            const audioBlob = new Blob(audioChunks.current, {
              type: "audio/webm",
            });
            const formData = new FormData();
            formData.append("file", audioBlob);
            const response = await api.post("/api/v1/transcribe", formData);
            const transcriptionResult = response.data;
            if (response.status !== 200 || !transcriptionResult.text) {
              throw new Error("Failed to transcribe answer");
            }
            const transcribedText = transcriptionResult.text?.trim();
            if (transcribedText) {
              try {
                // Update local questionsState with transcribed answer
                setQuestionsState((prev) => {
                  const updated = [...prev];
                  if (updated[currentIndex]) {
                    updated[currentIndex].answer = transcribedText;
                  }
                  localStorage.setItem("questions", JSON.stringify(updated));
                  return updated;
                });
                await updateAnswer(transcribedText);
              } catch (error) {
                throw new Error("Failed to save transcribed answer");
              }
            } else {
              toast.error(
                "No speech detected - please speak clearly and try again",
                {
                  id: "transcription-toast",
                  duration: 4000,
                }
              );
              return false;
            }
          }
        }
        setHasNarrated(false);
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }
        if (audioElement.current) {
          audioElement.current.pause();
          audioElement.current.src = "";
        }
        audioChunks.current = [];
        const currentAnswer = currentQuestion?.answer;
        if (currentAnswer !== undefined) {
          await updateAnswer(currentAnswer);
        }
        if (
          videoRef.current &&
          questionsState &&
          typeof currentIndex === "number" &&
          questionsState[currentIndex]
        ) {
          const time = videoRef.current.currentTime || 0;
          const updated = [...questionsState];
          updated[currentIndex].timestamp = time;
          localStorage.setItem("questions", JSON.stringify(updated));
          setQuestionsState(updated);
        }
        await stopRecognition();

        await onNextQuestion();
        return true;
      } catch (error) {
        toast.dismiss("transcription-toast");
        if (error.message === "Failed to transcribe answer") {
          toast.error(
            "Could not understand the answer. Please try speaking clearly."
          );
        } else if (error.message === "Failed to save typed answer") {
          toast.error("Failed to save your typed answer. Please try again.");
        } else if (
          error.message.includes("network") ||
          error.message.includes("fetch")
        ) {
          toast.error(
            "Network error. Please check your connection and try again."
          );
        } else {
          toast.error("Failed to process answer. Please try again.");
        }
        return false;
      } finally {
        setIsProcessing(false);
        audioChunks.current = [];
      }
    },
    [
      isProcessing,
      isSpeaking,
      isListening,
      currentQuestion,
      updateAnswer,
      stopRecognition,
      onNextQuestion,
      questions,
      currentIndex,
    ]
  );

  const handleEndInterview = useCallback(async () => {
    try {
      if (isProcessing || isSpeaking) {
        toast.error("Please wait for current answer to be processed");
        return;
      }

      // Don't skip saving if lastAnswerSavedRef is true
      // We want to ensure the answer is always saved
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
      timerExpirationInProgress.current = true;
      if (audioElement.current) {
        audioElement.current.pause();
        audioElement.current.src = "";
      }
      setIsSpeaking(false);
      if (isListening) {
        await stopRecognition();
      }

      // Ensure any remaining audio stream is stopped
      if (audioStreamRef.current) {
        console.log("Cleaning up remaining audio stream in handleEndInterview");
        audioStreamRef.current.getTracks().forEach(track => {
          track.stop();
          console.log("Stopped remaining audio track:", track.kind);
        });
        audioStreamRef.current = null;
      }
      if (audioChunks.current.length > 0) {
        const audioBlob = new Blob(audioChunks.current, { type: "audio/webm" });
        try {
          const transcriptionSuccess = await transcribeAudio(audioBlob);
          if (transcriptionSuccess) {
            lastAnswerSavedRef.current = true;
          }
          if (!transcriptionSuccess) {
            toast.error(
              "Could not transcribe final answer, but ending interview."
            );
          }
        } catch (transcriptionError) {
          toast.error(
            "Could not transcribe final answer, but ending interview."
          );
        } finally {
          audioChunks.current = [];
        }
      } else if (currentQuestion?.type?.toLowerCase() === "verbal") {
        // For verbal questions without audio, save an empty string
        await updateAnswer("");
        lastAnswerSavedRef.current = true;
      }
      await new Promise((resolve) => setTimeout(resolve, 1000));
      onEndInterview(questions);
    } catch (error) {
      toast.error("Error saving answers. Please try again.");
    } finally {
      timerExpirationInProgress.current = false;
    }
  }, [
    stopRecognition,
    onEndInterview,
    questions,
    currentIndex,
    isListening,
    isProcessing,
    isSpeaking,
    transcribeAudio,
  ]);

  useEffect(() => {
    if (
      isInterviewActive &&
      !isSpeaking &&
      !isProcessing &&
      remainingTime > 0 &&
      !timerRef.current &&
      !timerExpirationInProgress.current
    ) {
      const timer = setInterval(() => {
        setRemainingTime((prevTime) => {
          if (!isInterviewActive) return 0; // Prevent timer after interview ends
          if (prevTime <= 1) {
            handleNextQuestion();
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);
      timerRef.current = timer;
    } else if (
      (!isInterviewActive ||
        isSpeaking ||
        isProcessing ||
        timerExpirationInProgress.current) &&
      timerRef.current
    ) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [isSpeaking, isProcessing, remainingTime, isInterviewActive]);

  useEffect(() => {
    if (
      remainingTime === 0 &&
      !isProcessing &&
      !isSpeaking &&
      !timerExpirationInProgress.current &&
      !isTransitioning
    ) {
      const isLastQuestion = currentIndex >= questions.length - 1;
      if (isLastQuestion) {
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }
        timerExpirationInProgress.current = false;
        return;
      }
      timerExpirationInProgress.current = true;
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
      setTimeout(async () => {
        try {
          await handleNextQuestion();
        } catch (error) {
          toast.error("Failed to move to next question");
        } finally {
          timerExpirationInProgress.current = false;
        }
      }, 500);
    }
  }, [
    remainingTime,
    isProcessing,
    isSpeaking,
    currentIndex,
    questions.length,
    isTransitioning,
  ]);

  const handleSpeechRecognition = useCallback(async () => {
    if (isListening) return;
    try {
      startAnswering();
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

      // Store the stream reference for cleanup
      audioStreamRef.current = stream;

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: "audio/webm",
        audioBitsPerSecond: 128000,
      });
      mediaRecorderRef.current = mediaRecorder;
      audioChunks.current = [];
      mediaRecorder.ondataavailable = (event) => {
        audioChunks.current.push(event.data);
      };
      mediaRecorder.onstop = async () => {
        // Stop the audio stream tracks when recording stops
        if (audioStreamRef.current) {
          audioStreamRef.current.getTracks().forEach(track => {
            track.stop();
            console.log("Stopped audio track after recording:", track.kind);
          });
          audioStreamRef.current = null;
        }

        if (audioChunks.current.length > 0) {
          const audioBlob = new Blob(audioChunks.current, {
            type: "audio/webm",
          });
          await transcribeAudio(audioBlob).catch(() => {
            toast.error("Failed to transcribe audio");
          });
        }
      };
      mediaRecorder.start(1000);
      setIsListening(true);
    } catch (error) {
      toast.error("Failed to start recording");
      throw error;
    }
  }, [isListening, startAnswering, transcribeAudio]);

  return (
    <InterviewSectionContent
      localCamStream={localCamStream}
      videoRef={videoRef}
      questions={questionsState}
      currentQuestion={currentQuestion}
      currentIndex={currentIndex}
      isSpeaking={isSpeaking}
      isListening={isListening}
      isProcessing={isProcessing}
      isInterviewActive={isInterviewActive}
      isTransitioning={isTransitioning}
      error={error}
      remainingTime={remainingTime}
      isLoading={isLoading}
      handleSpeechRecognition={handleSpeechRecognition}
      handleNextQuestion={handleNextQuestion}
      handleEndInterview={handleEndInterview}
      startAnswering={startAnswering}
      updateAnswer={updateAnswer}
    />
  );
}
