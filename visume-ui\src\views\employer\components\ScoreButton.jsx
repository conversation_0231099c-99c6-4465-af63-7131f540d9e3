import React, { useState } from "react";

const ScoreButton = ({ overallScore, skillScore, communicationScore }) => {
  const [isHovered, setIsHovered] = useState(false);

  const getBorderColor = (score) => {
    if (score >= 8) return "border-green-500 text-green-500";
    if (score >= 7) return "border-yellow-500 text-yellow-500";
    if (score >= 5) return "border-orange-500 text-orange-500";
    return "border-gray-300 text-gray-700";
  };

  return (
    <div
      className="relative inline-block"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <button
        className={`h-10 w-10 rounded-full border-2 text-sm font-semibold 
          ${getBorderColor(overallScore)}`}
      >
        {overallScore}
      </button>

      {isHovered && (
        <div
          className="absolute -top-1 left-1/2 z-10 flex w-max -translate-x-1/2 transform cursor-pointer flex-col whitespace-nowrap rounded-lg border 
                     border-gray-200 bg-white p-3 opacity-100 shadow-lg transition-opacity duration-300 ease-in-out"
        >
          <div className="flex flex-row items-center space-x-4">
            <div className="flex flex-col items-center justify-center text-center">
              <span className="text-xs font-semibold">Skill</span>
              <button
                className={`h-8 w-8 rounded-full border-2 text-xs font-semibold
                ${getBorderColor(skillScore)}`}
              >
                {skillScore}
              </button>
            </div>

            <div className="flex flex-col items-center">
              <span className="text-xs font-semibold">Communication</span>
              <button
                className={`h-8 w-8 rounded-full border-2 text-xs font-semibold
                ${getBorderColor(communicationScore)}`}
              >
                {communicationScore}
              </button>
            </div>

            <div className="flex flex-col items-center">
              <span className="text-xs font-semibold">Overall</span>
              <button
                className={`h-8 w-8 rounded-full border-2 text-xs font-semibold
                ${getBorderColor(overallScore)}`}
              >
                {overallScore}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ScoreButton;
