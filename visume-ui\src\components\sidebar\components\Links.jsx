/* eslint-disable */
import React from "react";
import { Link, useLocation } from "react-router-dom";
import DashIcon from "components/icons/DashIcon";

export function SidebarLinks(props) {
  let location = useLocation();
  const layout = `/${location.pathname.split("/")[1]}`;
  let { routes, onClose, open } = props;

  // Verifies if routeName is the one active
  const activeRoute = (routeName) => {
    return location.pathname.includes(routeName);
  };

  const createLinks = (routes) => {
    return routes
      .filter((route) => route.layout === layout && !route.special && route.name)
      .map((route, index) => {
        const linkPath =
          layout === "/employer" && route.path === "profile-search"
            ? "/profile-search"
            : `${route.layout}/${route.path}`;

        const isActive = activeRoute(route.path);

        return (
          <Link
            key={index}
            to={linkPath}
            onClick={() => {
              // Only hide sidebar on smaller screens
              if (window.innerWidth < 768 && open) onClose();
            }}
            className="block"
          >
            <div className={`
              group relative flex items-center px-3 py-2.5 mx-1 rounded-lg text-sm font-medium transition-all duration-200 ease-in-out
              ${isActive
                ? "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 shadow-sm"
                : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
              }
            `}>
              {/* Icon */}
              <div className={`
                flex items-center justify-center w-5 h-5 mr-3 transition-colors duration-200
                ${isActive
                  ? "text-blue-600 dark:text-blue-400"
                  : "text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300"
                }
              `}>
                {route.icon ? route.icon : <DashIcon />}
              </div>

              {/* Text */}
              <span className={`
                flex-1 truncate transition-colors duration-200
                ${isActive
                  ? "text-blue-700 dark:text-blue-300 font-semibold"
                  : "text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white"
                }
              `}>
                {route.name}
              </span>

              {/* Active indicator */}
              {isActive && (
                <div className="absolute right-2 w-1.5 h-1.5 bg-blue-600 dark:bg-blue-400 rounded-full" />
              )}

              {/* Hover effect overlay */}
              <div className={`
                absolute inset-0 rounded-lg opacity-0 transition-opacity duration-200
                ${!isActive ? "group-hover:opacity-100 bg-gradient-to-r from-transparent via-gray-50 to-transparent dark:via-gray-800" : ""}
              `} />
            </div>
          </Link>
        );
      });
  };

  return (
    <div className="space-y-1">
      {createLinks(routes)}
    </div>
  );
}

export default SidebarLinks;
