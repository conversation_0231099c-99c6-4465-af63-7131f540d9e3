import React from "react";
import { Routes, Route, Navigate, useLocation } from "react-router-dom";
import Navbar from "components/navbar";
import Sidebar from "components/sidebar";
import Footer from "components/footer/Footer";
import routes from "routes";

export default function Candidate(props) {
  const { ...rest } = props;

  const location = useLocation();
  const [open, setOpen] = React.useState(window.innerWidth >= 1280);
  const [currentRoute, setCurrentRoute] = React.useState("Home");

  

  React.useEffect(() => {
    const handleResize = () => {
      setOpen(window.innerWidth >= 1280);
    };

    window.addEventListener('resize', handleResize);
    
    // Cleanup on unmount
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  

  React.useEffect(() => {
    getActiveRoute(routes);
  }, [location.pathname]);

  const getActiveRoute = (routes) => {

    let activeRoute = "Home";
    
    for (let i = 0; i < routes.length; i++) {       
    if (window.location.href.indexOf(routes[i].layout + "/" + routes[i].path)!== -1){
        setCurrentRoute(routes[i].name);
      }
    }
    return activeRoute;
  };

  const getActiveNavbar = (routes) => {
    let activeNavbar = false;
    for (let i = 0; i < routes.length; i++) {
      if (window.location.href.indexOf(routes[i].layout + routes[i].path) !== -1) {
        return routes[i].secondary;
      }
    }
    return activeNavbar;
  };

  const getRoutes = (routes) => {
    return routes.map((prop, key) => {
      if (prop.layout === "/candidate") {
        return (
          <Route path={`/${prop.path}`}  element={prop.component} key={key} />
        );
      } else {
        return null;
      } 
    });
  };

  document.documentElement.dir = "ltr";
  return (
    <div className="flex h-screen w-full bg-gray-50 dark:bg-gray-900">
      <Sidebar open={open} onClose={() => {if(window.innerWidth < 1280){setOpen(false);} }}/>
      
      {/* Main Content Container */}
      <div className={`flex-1 flex flex-col transition-all duration-300 ease-in-out ${
        open ? 'xl:ml-64' : 'xl:ml-0'
      }`}>
        {/* Navbar */}
        <Navbar
          onOpenSidenav={() => setOpen(!open)}
          logoText={"Horizon UI Tailwind React"}
          brandText={currentRoute}
          secondary={getActiveNavbar(routes)}
          {...rest}
        />
        
        {/* Main Content Area */}
        <main className="flex-1 overflow-auto bg-lightPrimary dark:bg-navy-900">
          <div className="container mx-auto px-4 py-6 max-w-7xl">
            <Routes>
              {getRoutes(routes)}
              <Route path="/" element={<Navigate to="/candidate/dashboard" replace />}/>
            </Routes>
          </div>
          
          <Footer />
        </main>
      </div>
    </div>
  );
}
