// ProfileHeader.jsx
import React, { useState } from "react";
import {
  MapPin,
  IndianRupee,
  Star,
  MessageCircle,
  BarChart2,
} from "lucide-react";
import Progress from "components/progress";
import toast from "react-hot-toast";

function ScoreCard({ icon, title, score, color }) {
  return (
    <div className="flex items-center justify-between rounded-lg border border-gray-100 bg-gradient-to-br from-white to-gray-50 p-2 shadow min-w-[160px]">
      <div className="flex items-center gap-2">
        <div className={`flex items-center justify-center h-8 w-8 rounded-lg bg-${color}-50`}>
          {icon}
        </div>
        <div>
          <div className="text-sm font-medium text-gray-800">{title}</div>
          <div className="flex items-center gap-1">
            <span className="text-base font-semibold text-gray-900">{score}</span>
            <span className={`inline-block rounded px-1.5 py-0.5 text-[10px] font-normal
              ${color === "yellow" ? "bg-yellow-100 text-yellow-700"
              : color === "blue" ? "bg-blue-100 text-blue-700"
              : color === "green" ? "bg-green-100 text-green-700"
              : "bg-gray-100 text-gray-700"}`}>
              {score >= 80 ? "Excellent" : score >= 60 ? "Good" : "Average"}
            </span>
          </div>
        </div>
      </div>
      <div className="flex flex-col items-center justify-center h-full ml-2">
        <Progress value={score} color={color} width="h-16 w-1.5" />
      </div>
    </div>
  );
}

const ProfileHeader = ({
  candidateProf,
  profileData,
  strippedResumeJson,
  questionsAndAnswers, // Added prop
  videoRef, // NEW: video ref for controlling playback
}) => {
  const [shortlistLoading, setShortlistLoading] = useState(false);
  const [shortlisted, setShortlisted] = useState(false);

  // Log IDs on initial render for verification (remove after checking)
  React.useEffect(() => {
    console.log("ProfileHeader IDs:", {
      emp_id: candidateProf?.emp_id,
      cand_id: candidateProf?.cand_id,
      video_profile_id: candidateProf?.video_profile_id,
    });
  }, []);

  // Shortlist handler
  const handleShortlist = async () => {
    if (!candidateProf?.cand_id || !candidateProf?.emp_id) {
      toast.error("Candidate ID or Employer ID not found.");
      return;
    }
    setShortlistLoading(true);
    try {
      const emp_id = candidateProf.emp_id;
      const cand_id = candidateProf.video_profile_id;
      console.log(
        "Shortlist API call - emp_id:",
        emp_id,
        "cand_id (video_profile_id):",
        cand_id
      );
      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/employer-profiles/shortlist-candidate`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ emp_id, cand_id }),
        }
      );
      const data = await response.json();
      if (!response.ok) {
        toast.error(data?.message || "Failed to shortlist candidate");
        return;
      }
      setShortlisted(true);
      toast.success(data?.message || "Candidate shortlisted!");
    } catch (error) {
      toast.error(error.message || "Failed to shortlist candidate");
    } finally {
      setShortlistLoading(false);
    }
  };

  return (
    <div className="max-w-full space-y-2  p-0">
      <div className="flex flex-col gap-4 rounded-2xl bg-white p-6 shadow">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div className="flex items-center gap-4">
            <div className="flex h-14 w-14 flex-shrink-0 items-center justify-center rounded-full border border-brand-100 bg-brand-50">
              <svg
                className="h-8 w-8 text-brand-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"
                />
              </svg>
            </div>
            <div>
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold text-gray-900">
                  {candidateProf.cand_name}
                </span>
              </div>
              <div className="mt-1 flex flex-wrap items-center gap-3">
                <span className="flex items-center text-sm text-gray-600">
                  <MapPin className="mr-1 h-4 w-4 text-brand-500" />
                  {candidateProf.preferred_location}
                </span>
                <span className="flex items-center text-sm text-gray-600">
                  <IndianRupee className="mr-1 h-4 w-4 text-green-600" />
                  <span>
                    <span className="font-semibold">Current:</span>{" "}
                    {JSON.parse(profileData.salary).current || "N/A"} LPA
                  </span>
                  <span className="mx-1 text-gray-400">|</span>
                  <span>
                    <span className="font-semibold">Expected:</span>{" "}
                    {JSON.parse(profileData.salary).expected || "N/A"} LPA
                  </span>
                </span>
              </div>
            </div>
          </div>
          <div className="flex min-w-[180px] flex-col gap-2">
            <span className="flex items-center gap-2 text-lg font-semibold text-brand-600">
              <BarChart2 className="h-5 w-5 text-brand-600" />
              {profileData.role}
            </span>
            <span className="flex items-center gap-2 font-medium text-gray-700">
              <Star className="h-5 w-5 text-yellow-500" />
              <span>Skills:</span>
              <span className="flex flex-wrap gap-1">
                {profileData.skills ? (
                  profileData.skills.split(",").map((skill, idx) => (
                    <span
                      key={idx}
                      className="inline-block rounded border border-blue-100 bg-blue-50 px-1.5 py-0.5 text-[11px] font-normal text-blue-700"
                    >
                      {skill.trim()}
                    </span>
                  ))
                ) : (
                  <span className="text-gray-400">N/A</span>
                )}
              </span>
            </span>
          </div>
        </div>
        {profileData.video_url ? (
          <video
            ref={videoRef}
            controls={true}
            className="mt-4 h-[425px] w-full rounded-lg object-contain"
            src={profileData.video_url}
          />
        ) : (
          <div className="mt-4 flex h-[425px] w-full items-center justify-center rounded-lg bg-gray-500  text-white">
            No Video Url Found
          </div>
        )}
        <div className="mt-8 flex flex-col sm:flex-row gap-6 items-stretch justify-between">
          <div className="flex flex-1 flex-col gap-4">
            <h2 className="mb-2 text-xl font-bold text-brand-700 tracking-tight">
              Visume Scores
            </h2>
            <div className="flex flex-col gap-4">
              {profileData?.score && (
                <>
                  <ScoreCard
                    icon={<Star className="h-6 w-6 text-yellow-500" />}
                    title="Skill"
                    score={JSON.parse(profileData.score)?.score?.Skill_Score || 0}
                    color="yellow"
                  />
                  <ScoreCard
                    icon={<MessageCircle className="h-6 w-6 text-blue-500" />}
                    title="Communication"
                    score={JSON.parse(profileData.score)?.score?.Communication_Score || 0}
                    color="blue"
                  />
                  <ScoreCard
                    icon={<BarChart2 className="h-6 w-6 text-green-500" />}
                    title="Overall"
                    score={JSON.parse(profileData.score)?.score?.Overall_Score || 0}
                    color="green"
                  />
                </>
              )}
            </div>
          </div>
          <div className="flex flex-col items-center justify-center">
            <div className="rounded-2xl bg-gradient-to-br from-brand-100 to-white shadow-xl p-6 flex flex-col items-center gap-3 border border-brand-200 min-w-[200px]">
              <span className="text-brand-700 font-semibold text-base mb-1">Take Action</span>
              <button
                className={`flex items-center justify-center rounded bg-brand-600 px-4 py-1.5 text-sm font-medium text-white shadow transition hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-brand-400 ${
                  shortlisted ? "cursor-not-allowed opacity-60" : ""
                }`}
                onClick={handleShortlist}
                disabled={shortlistLoading || shortlisted}
              >
                <Star className="mr-1 h-4 w-4" />
                {shortlistLoading
                  ? "Shortlisting..."
                  : shortlisted
                  ? "Shortlisted"
                  : "Shortlist"}
              </button>
            </div>
          </div>
        </div>
        {/* Professional Summary */}
        <div className="mt-4 border-t border-gray-200 pt-4">
          <h2 className="mb-2 flex items-center text-lg font-semibold">
            <span className="mr-2 inline h-5 w-5">
              <svg width="20" height="20">
                <rect width="20" height="20" fill="none" />
              </svg>
            </span>
            Professional Summary
          </h2>
          <p className="text-sm text-gray-600">
            {strippedResumeJson?.professionalSummary || "N/A"}
          </p>
        </div>
      </div>
      {/* Questions and Answers Section */}
    </div>
  );
};

export default ProfileHeader;
