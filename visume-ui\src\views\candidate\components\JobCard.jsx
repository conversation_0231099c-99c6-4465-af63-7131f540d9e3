import React from 'react'
import { MapPin, Building2, Users, ExternalLink } from 'lucide-react'

const JobCard = ({job, iconUrl}) => {
  return (
    <div
      className="group relative bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-lg p-3 hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-md transition-all duration-200 cursor-pointer"
      onClick={() => window.open(job.url, '_blank', 'noopener,noreferrer')}
    >
      {/* Compact Header */}
      <div className="flex items-center gap-2 mb-2">
        <div className="relative flex-shrink-0">
          <img
            src={iconUrl}
            alt={job.company}
            className="w-8 h-8 rounded object-contain bg-gray-50 dark:bg-gray-800 p-1"
            onError={(e) => {
              e.target.style.display = 'none';
              e.target.nextSibling.style.display = 'flex';
            }}
          />
          <div className="hidden w-8 h-8 rounded bg-gradient-to-br from-blue-500 to-purple-600 items-center justify-center text-white font-bold text-xs">
            {job.company?.charAt(0)?.toUpperCase()}
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-semibold text-gray-900 dark:text-white truncate group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
            {job.title}
          </h3>
          <p className="text-xs text-gray-600 dark:text-gray-400 truncate">{job.company}</p>
        </div>
        <ExternalLink className="w-3 h-3 text-gray-400 group-hover:text-blue-500 transition-colors opacity-0 group-hover:opacity-100 flex-shrink-0" />
      </div>

      {/* Compact Details */}
      <div className="space-y-1">
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400">
            <Users className="w-3 h-3" />
            <span className="font-medium text-gray-900 dark:text-white">{job.openings}</span>
            <span>opening{job.openings !== '1' ? 's' : ''}</span>
          </div>
          {job.type && (
            <span className="px-1.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300">
              {job.type}
            </span>
          )}
        </div>
        
        {job.location && (
          <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
            <MapPin className="w-3 h-3" />
            <span className="truncate">{job.location}</span>
          </div>
        )}
      </div>
    </div>
  )
}

export default JobCard