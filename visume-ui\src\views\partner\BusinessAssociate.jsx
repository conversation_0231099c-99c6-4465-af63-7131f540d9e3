import React, { useEffect } from "react";
import JobCard from "./components/JobCard";
import { useState } from "react";
import Cookies from "js-cookie";
import { useNavigate } from "react-router-dom";
import avatar from "assets/img/avatars/avatar4.png";
import { HiOutlineSparkles } from "react-icons/hi";
import toast from "react-hot-toast";

const BusinessAssociate = () => {
  const navigate = useNavigate();
  const getFormDataFromCookie = () => {
    const cookieData = Cookies.get("formData");
    cookieData
      ? toast.success(cookieData)
      : toast.error("no video profile created");
  };
  const jstoken = Cookies.get("jstoken");
  const emp_id = Cookies.get("emp_id");
  const company_id = Cookies.get("emp_id");
  const designation = Cookies.get("emp_id");

  const jobData = [
    {
      id: 1,
      name: "<PERSON>",
      title: "Software Engineer",
      image: "assets/img/avatars/avatar1.png",
      video_profile: "",
    },
    {
      id: 2,
      name: "<PERSON>",
      title: "Frontend Developer",
      image: "assets/img/avatars/avatar3.png",
      video_profile: "",
    },
    {
      id: 3,
      name: "Sophia <PERSON>",
      title: "Backend Developer",
      image: "assets/img/avatars/avatar10.png",
      video_profile: "",
    },
    {
      id: 4,
      name: "James Anderson",
      title: "Fullstack Developer",
      image: "assets/img/avatars/avatar11.png",
      video_profile: "",
    },
  ];

  const stats = {
    interviewsAttended: 5,
    shortlists: 3,
    videoProfilesCreated: 10,
  };

  const [videoProfiles, setVideoProfiles] = useState([]);

  useEffect(() => {
    // Get the values from cookies
    const fetchVideoProfiles = async () => {
      try {
        // Make the API request with the dynamic emp_id
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/employer-profiles/${emp_id}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );
      } catch (error) {
        console.error("Network error:", error);
      }
    };
  }, [emp_id]);

  return (
    <>
      {jstoken ? (
        <div className="mt-3 grid grid-cols-12 gap-x-4 gap-y-4">
          {/* Start Intro */}
          <div className="card col-span-full rounded-md bg-white p-0 dark:bg-navy-700  dark:text-white lg:col-span-8 2xl:col-span-7">
            <div className="relative grid h-full  grid-cols-12 overflow-hidden rounded-lg bg-white px-3 py-6 shadow-md dark:bg-navy-700 dark:text-white sm:px-12">
              <div className="col-span-full inline-flex flex-col items-start justify-center space-y-3 md:col-span-7">
                <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
                  Welcome Back, <span className="text-brand-500">{emp_id}</span>
                </h1>
                <p className="text-sm text-gray-600">
                  {company_id}, {designation}
                </p>
              </div>

              <div className="col-span-full items-center justify-end md:col-span-5 md:flex">
                <img
                  src="https://www.jobma.com/blog/wp-content/uploads/2013/08/Video-Resumes-The-Importance-of-Audio-1-300x217.png"
                  alt="Illustration for Video Resume"
                  className="h-auto w-[200px] object-cover"
                />
              </div>
            </div>
          </div>

          <div className="card col-span-full rounded-md bg-white p-6 shadow-lg dark:bg-navy-700 dark:text-white lg:col-span-4 2xl:col-span-5">
            {/* Top User Section */}
            <div className="mb-5 flex items-center justify-start gap-10">
              <div className="flex items-center">
                <img
                  src={avatar}
                  alt="User Avatar"
                  className="mr-4 h-14 w-14 rounded-full"
                />
                <div className="flex flex-col items-start gap-1">
                  <h3 className="text-xl font-bold">Tejas S P</h3>
                  <span className=" inline-flex items-center rounded-full bg-brand-200 px-2 py-1 text-[10px] font-semibold text-white">
                    PRO Plan
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex gap-2 text-sm font-medium">
                <button className="rounded-lg bg-gray-100 px-4 py-2 hover:bg-gray-200 dark:bg-navy-600">
                  Shortlisted{" "}
                  <span className="font-bold">{stats.shortlists}</span>
                </button>
                <button className="rounded-lg bg-gray-100 px-4 py-2 hover:bg-gray-200 dark:bg-navy-600">
                  Unlocked{" "}
                  <span className="font-bold">{stats.interviewsAttended}</span>
                </button>
              </div>
            </div>
          </div>

          {/* End Intro */}

          {/* Start Short Progress Card */}
          <div className="card col-span-full h-fit rounded-md bg-white p-0 dark:bg-navy-700 dark:text-white lg:col-span-7 2xl:col-span-7">
            <h2 className="flex items-center gap-2 px-5 pt-5 text-lg font-semibold text-gray-800 dark:text-white sm:px-9">
              <span>
                <HiOutlineSparkles className="text-lg text-gray-700" />
              </span>
              Shortlisted Video Profiles
            </h2>

            <div className="grid grid-cols-12 gap-4 px-5 py-5 sm:px-9">
              {jobData.map((job) => (
                <JobCard
                  iconUrl="https://static-00.iconduck.com/assets.00/microsoft-icon-2048x2048-xtoxrveo.png"
                  key={job.id}
                  job={job}
                />
              ))}
            </div>
          </div>

          <div className="card col-span-full h-fit rounded-md bg-white p-0 dark:bg-navy-700 dark:text-white lg:col-span-7 2xl:col-span-7">
            <h2 className="flex items-center gap-2 px-5 pt-5 text-lg font-semibold text-gray-800 dark:text-white sm:px-9">
              <span>
                <HiOutlineSparkles className="text-lg text-gray-700" />
              </span>
              Unlocked Video Profiles
            </h2>

            <div className="grid grid-cols-12 gap-4 px-5 py-5 sm:px-9">
              {jobData.map((job) => (
                <JobCard
                  iconUrl="https://static-00.iconduck.com/assets.00/microsoft-icon-2048x2048-xtoxrveo.png"
                  key={job.id}
                  job={job}
                />
              ))}
            </div>
          </div>
        </div>
      ) : (
        <h2>lol signin</h2>
      )}
    </>
  );
};

export default BusinessAssociate;
