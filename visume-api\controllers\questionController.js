const { generateSingleQuestion } = require("../utils/helpers");

exports.generateNextQuestion = async (req, res) => {
  const MIN_QUESTIONS = 5;
  
  try {
    const { role, skills, previousQuestions, companyType, experience } = req.body;

    // Validate required fields
    if (!role) {
      return res.status(400).json({
        success: false,
        message: "Role is required for question generation"
      });
    }

    // Parse and validate previous questions array
    // Frontend sends previousQuestions as an array of objects, each containing question and answer
    const previousQA = (previousQuestions || []).map((q) => ({
      question: q.question,
      answer: q.answer || null, // Directly use the answer from the question object
      type: q.type,
      question_number: q.question_number,
      total_questions: q.total_questions
    }));

    console.log("Previous Questions:", previousQA);

    // Always enforce strict maximum of 10 questions per session
    const totalQuestions = 10;
    const questionCount = (previousQuestions || []).length + 1;

    // Check if we've reached the total questions limit
    if (questionCount > totalQuestions) {
      return res.status(200).json({
        success: false,
        message: "Interview completed",
        completed: true
      });
    }

    console.log(`Generating question ${questionCount} of ${totalQuestions}`);

    // Enforce alternation/shuffling between behavioral and technical questions
    const isFirstQuestion = !previousQuestions || previousQuestions.length === 0;

    // Determine next type: alternate or shuffle
    let forcedType = null;
    if (isFirstQuestion) {
      forcedType = "behavioral";
    } else {
      // Count previous types
      const prevTypes = previousQA.map(q => q.type);
      const lastType = prevTypes[prevTypes.length - 1];
      // Alternate: if last was behavioral, now technical; if last was technical, now behavioral
      if (lastType === "behavioral") {
        forcedType = "technical";
      } else if (lastType === "technical") {
        forcedType = "behavioral";
      } else {
        // If unknown, randomly pick
        forcedType = Math.random() < 0.5 ? "behavioral" : "technical";
      }
    }

    const nextQuestion = await generateSingleQuestion(
      role,
      previousQA,
      skills,
      isFirstQuestion,
      companyType,
      experience,
      forcedType
    );

    // Validate and ensure timerDuration is within acceptable range
    const timerDuration = Math.min(90, Math.max(30, parseInt(nextQuestion.timerDuration) || 90));

    const response = {
      success: true,
      question: nextQuestion.question,
      type: nextQuestion.type,
      timerDuration: timerDuration,
      question_number: questionCount,
      total_questions: totalQuestions,
      completed: questionCount >= totalQuestions,
      _fallback: nextQuestion._fallback || false
    };

    res.status(200).json(response);

  } catch (error) {
    console.error("Error generating next question:", error);
    
    // Simple fallback
    const fallbackQuestion = {
      question: previousQA.length === 0
        ? `Tell me about your experience with ${req.body.role || 'software development'}.`
        : "Could you tell me more about your approach to problem-solving?",
      type: "behavioral",
      timerDuration: 90, // Default fallback duration
      _fallback: true
    };

    const status = error.message === "Interview completed" ? 200 : 500;
    res.status(status).json({
      success: status === 200,
      ...fallbackQuestion,
      completed: error.message === "Interview completed",
      error_details: error.message
    });
  }
};

exports.analyzeAnswer = async (req, res) => {
  try {
    const { question, answer, role, skills, previousQA } = req.body;

    if (!question || !role) {
      return res.status(400).json({
        message: "Question and role are required fields"
      });
    }

    // Import helpers
    const { analyzeAnswerAndGenerateFollowUp } = require("../utils/helpers");

    console.log("Analyzing answer:", {
      question,
      answerLength: answer?.length,
      role,
      skillsCount: skills?.length
    });

    // Get analysis and follow-up
    const analysisResult = await analyzeAnswerAndGenerateFollowUp(
      question,
      answer,
      role,
      skills,
      previousQA
    );

    console.log("Analysis completed:", {
      hasAnalysis: !!analysisResult.analysis,
      hasFollowUp: !!analysisResult.follow_up?.question
    });

    // Return the analysis result
    res.json({
      analysis: analysisResult.analysis,
      follow_up: analysisResult.follow_up
    });

  } catch (error) {
    console.error("Error analyzing answer:", error);
    res.status(500).json({
      message: "Failed to analyze answer",
      error: error.message,
      analysis: {
        technical_accuracy: "Analysis failed",
        communication: "Analysis failed",
        knowledge_gaps: ["Unable to analyze response"]
      },
      follow_up: {
        question: "Could you provide more details about your previous answer?",
        reasoning: "Default follow-up due to analysis error",
        expected_focus: "General elaboration"
      }
    });
  }
};
