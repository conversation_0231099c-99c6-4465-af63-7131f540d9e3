import React from "react";
import { Routes, Route, Navigate, useLocation } from "react-router-dom";
import Navbar from "components/navbar";
import Sidebar from "components/sidebar";
import Footer from "components/footer/Footer";
import routes from "routes";

export default function Candidate(props) {
  const { ...rest } = props;

  const location = useLocation();
  const [open, setOpen] = React.useState(window.innerWidth > 1200);
  const [currentRoute, setCurrentRoute] = React.useState("Dashboard");

  

  React.useEffect(() => {
    const handleResize = () => {
      setOpen(window.innerWidth > 1200);
    };

    window.addEventListener('resize', handleResize);
    
    // Cleanup on unmount
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  

  React.useEffect(() => {
    getActiveRoute(routes);
  }, [location.pathname]);

  const getActiveRoute = (routes) => {

    let activeRoute = "Dashboard";
    
    for (let i = 0; i < routes.length; i++) {       
    if (window.location.href.indexOf(routes[i].layout + "/" + routes[i].path)!== -1){
        setCurrentRoute(routes[i].name);
      }
    }
    return activeRoute;
  };

  const getActiveNavbar = (routes) => {
    let activeNavbar = false;
    for (let i = 0; i < routes.length; i++) {
      if (window.location.href.indexOf(routes[i].layout + routes[i].path) !== -1) {
        return routes[i].secondary;
      }
    }
    return activeNavbar;
  };

  const getRoutes = (routes) => {
    return routes.map((prop, key) => {
      if (prop.layout === "/admin") {
        return (
          <Route path={`/${prop.path}`}  element={prop.component} key={key} />
        );
      } else {
        return null;
      } 
    });
  };

  document.documentElement.dir = "ltr";
  return (
    <div className="flex h-full w-full">
      <Sidebar open={open} onClose={() => {if(window.innerWidth<768){setOpen(false);} }}/>
      {/* Navbar & Main Content */}
      <div className="h-full w-full bg-lightPrimary dark:!bg-navy-900">
        {/* Main Content */}
        <main className={`mx-[12px] h-full flex-none transition-all md:pr-2 xl:ml-[270px]`} >
          {/* Routes */}
          <div className="h-full">
            <Navbar
              onOpenSidenav={() => setOpen(!open)}
              logoText={"Horizon UI Tailwind React"}
              brandText={currentRoute}
              secondary={getActiveNavbar(routes)}
              {...rest}
            />

            

            <div className="pt-5s mx-auto mb-auto h-full min-h-[84vh] p-2 md:pr-2">
              <Routes>

                {getRoutes(routes)}

                <Route path="/" element={<Navigate to="/admin/dashboard" replace />}/>
                
              </Routes>

            </div>

              <Footer />

          </div>
        </main>
      </div>
    </div>
  );
}

