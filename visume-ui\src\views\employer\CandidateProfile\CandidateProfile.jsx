// CandidateProfile.jsx (refactored)
import React, { useEffect, useState, useRef } from "react";
import { useNavigate, useParams } from "react-router-dom";
import Loader from "components/Loader";
import CustomNavbar from "../components/CustomNavbar";
import {
  HiCollection,
  HiHome,
  HiOutlineSparkles,
  HiSearch,
  HiAcademicCap,
  HiBriefcase,
  HiBadgeCheck,
  HiStar,
  HiOutlineUser,
  HiOutlineGlobeAlt,
  HiOutlineDocumentText,
} from "react-icons/hi";
import { MdBarChart } from "react-icons/md";
import { LuIndianRupee } from "react-icons/lu";
import toast from "react-hot-toast";
import { AlertCircle, CreditCard, Lock, X, Mail, Phone } from "lucide-react";
import ProfileHeader from "./ProfileHeader";
import ProfileDetails from "./ProfileDetails";
import Sidebar from "./Sidebar";

const CandidateProfile = () => {
  const links = [
    { text: "Dashboard", url: "/employer/", icon: <HiHome /> },
    {
      text: "Track Candidates",
      url: "/employer/track-candidates",
      icon: <MdBarChart className="h-6 w-6" />,
    },
    {
      text: (
        <span className="flex items-center">
          Source with AI{" "}
          <span className="ml-2 rounded-full bg-orange-400 px-2 text-xs font-semibold text-white">
            Beta
          </span>
        </span>
      ),
      url: "/employer",
      icon: <HiOutlineSparkles />,
      className: "text-orange-400 font-bold hover:text-orange-500",
    },
  ];
  const [subPopup, setSubPopup] = useState(false);
  const [detailsBlur, setDetailsBlur] = useState(true);
  const [profileData, setProfileData] = useState(null);
  const [questionsAndAnswers, setQuestionsAndAnswers] = useState([]);
  // Video ref for controlling playback
  const videoRef = useRef(null);

  // Handler to seek and play video at a given timestamp or ISO string
  const handleQuestionClick = (qa) => {
    let seekTime = 0;
    if (
      qa &&
      qa.startTimestamp &&
      Array.isArray(questionsAndAnswers) &&
      questionsAndAnswers.length > 0 &&
      questionsAndAnswers[0].startTimestamp
    ) {
      const base = new Date(questionsAndAnswers[0].startTimestamp);
      const target = new Date(qa.startTimestamp);
      if (!isNaN(base.getTime()) && !isNaN(target.getTime())) {
        seekTime = Math.max(0, (target.getTime() - base.getTime()) / 1000);
      }
    }
    if (videoRef.current) {
      videoRef.current.currentTime = seekTime;
      videoRef.current.play();
    }
  };
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  let { vpid } = useParams();
  const [candidateProf, setCandidateProf] = useState(null);
  const [strippedResumeJson, setStrippedResumeJson] = useState(null);

  // Q&A navigation state (moved from inside render)
  const [currentQAIndex, setCurrentQAIndex] = useState(0);

  // Sidebar open state for mobile/tablet
  const [sidebarOpen, setSidebarOpen] = useState(false);
  console.log("Sidebar state at declaration:", sidebarOpen);

  // Debug: Log sidebar open state at top of render
  console.log("Render CandidateProfile", sidebarOpen);

  // Detect if screen is small (tailwind: < lg)
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  useEffect(() => {
    const checkScreen = () => setIsSmallScreen(window.innerWidth < 1024);
    checkScreen();
    window.addEventListener("resize", checkScreen);
    return () => window.removeEventListener("resize", checkScreen);
  }, []);

  // Prevent background scroll when sidebar is open on small screens
  useEffect(() => {
    if (isSmallScreen && sidebarOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [isSmallScreen, sidebarOpen]);
  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/video-resume-data/${vpid}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        const data = await response.json();
        // TEMP LOG: Inspect API response and questionsAndAnswers
        console.log(
          "Full API response from /api/v1/video-resume-data/:vpid:",
          data
        );
        console.log(
          "questionsAndAnswers from API response:",
          data?.questionsAndAnswers
        );
        setProfileData(data.data);
        // Use backend-provided timestamps only
        console.log(
          "DEBUG: questionsAndAnswers from API:",
          data.questionsAndAnswers
        );
        setQuestionsAndAnswers(data.questionsAndAnswers || []);

        const additionalResponse = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${
            data.data.cand_id
          }`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );
        if (!additionalResponse.ok) {
          throw new Error("Network response for additional data was not ok");
        }
        const additionalData = await additionalResponse.json();
        const candidateProfile = additionalData.candidateProfile[0];
        // Add emp_id from cookies and video_profile_id from vpid
        const emp_id =
          window.Cookies?.get("employerId") ||
          (await import("js-cookie")).default.get("employerId");
        setCandidateProf({
          ...candidateProfile,
          emp_id,
          video_profile_id: vpid || data.data.id,
        });
        // TEMP LOG: Inspect candidateProf structure
        console.log("candidateProf structure:", {
          ...candidateProfile,
          emp_id,
          video_profile_id: vpid || data.data.id,
        });

        const stripped_resume_json = candidateProfile.stripped_resume;
        setStrippedResumeJson(stripped_resume_json);
        // TEMP LOG: Inspect strippedResumeJson structure
        console.log("strippedResumeJson structure:", stripped_resume_json);
        if (!stripped_resume_json) {
          toast.error("Resume Not Uploaded Properly Details are Missing.");
        }
      } catch (error) {
        console.error("Failed to fetch profile data:", error);
        setError("Failed to fetch profile data");
      } finally {
        setLoading(false);
      }
    };

    if (vpid) {
      fetchProfileData();
    }
  }, [vpid]);

  const profiles = [
    {
      name: "John Doe",
      role: "Front-End Developer",
      rating: 4.5,
      image: "https://via.placeholder.com/50",
    },
    {
      name: "Jane Smith",
      role: "UX Designer",
      rating: 4.8,
      image: "https://via.placeholder.com/50",
    },
    {
      name: "Alex Johnson",
      role: "Full-Stack Developer",
      rating: 4.2,
      image: "https://via.placeholder.com/50",
    },
  ];
  const navigate = useNavigate();

  if (loading) return <Loader />;
  if (!profileData || !candidateProf) return null;

  return (
    <div className="min-h-screen bg-gray-100 p-3">
      <CustomNavbar links={links} />
      <div className="mx-auto">
        <div className="min-h-screen bg-gray-100  p-4">
          {/* Back Button */}
          <div className="mx-6 mb-2 max-w-3xl">
            <button
              onClick={() => {
                const previousUrl = localStorage.getItem("previousUrl");
                if (previousUrl) {
                  window.location.href = previousUrl;
                } else {
                  navigate("/profile-search/filterCandidate");
                }
              }}
              className="flex items-center text-sm font-semibold text-brand-600"
            >
              &larr; Back to Candidates
            </button>
          </div>

          {/* Sidebar open button for small screens */}
          {isSmallScreen && (
            <div className="mb-4 flex justify-end">
              <button
                className="rounded-md bg-brand-600 px-4 py-2 text-white shadow hover:bg-brand-700"
                onClick={() => {
                  console.log(
                    "[Sidebar] Hamburger/menu button clicked: opening sidebar"
                  );
                  setSidebarOpen(true);
                }}
                aria-label="Open sidebar"
              >
                Show Similar Profiles
              </button>
            </div>
          )}

          <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:space-y-0">
            {/* Left Column */}
            <div className="space-y-4">
              <ProfileHeader
                candidateProf={candidateProf}
                profileData={profileData}
                strippedResumeJson={strippedResumeJson}
                questionsAndAnswers={questionsAndAnswers}
                videoRef={videoRef}
              />
              {/* Profile Details Section replaced with strippedResumeJson details */}
              <div className="mt-6 grid gap-6 md:grid-cols-2">
                {candidateProf?.stripped_resume ? (
                  <div className="col-span-2 flex flex-col gap-6">
                    {/* Personal Info */}
                    {strippedResumeJson && (
                      <div className="relative">
                        <div
                          className={`rounded-2xl border border-gray-200 bg-gradient-to-br from-gray-50 to-white p-6 shadow transition-all ${
                            detailsBlur
                              ? "pointer-events-none select-none blur-md filter"
                              : ""
                          }`}
                          style={{ minHeight: "120px" }}
                        >
                          <div className="relative flex overflow-hidden rounded-2xl border border-gray-200 bg-gradient-to-br from-gray-50 to-white shadow-lg">
                            <div className="w-2 rounded-l-2xl bg-brand-600/80" />
                            <div className="flex-1 p-6">
                              <dl className="divide-y divide-gray-200">
                                {strippedResumeJson?.name && (
                                  <div className="flex items-center gap-4 py-3">
                                    <HiOutlineUser className="h-5 w-5 text-brand-600" />
                                    <dt className="min-w-[70px] font-semibold text-gray-700">
                                      Name
                                    </dt>
                                    <dd className="font-medium text-gray-900">
                                      {strippedResumeJson.name}
                                    </dd>
                                  </div>
                                )}
                                {strippedResumeJson?.email && (
                                  <div className="flex items-center gap-4 py-3">
                                    <Mail className="h-5 w-5 text-blue-600" />
                                    <dt className="min-w-[70px] font-semibold text-gray-700">
                                      Email
                                    </dt>
                                    <dd className="break-all font-medium text-gray-900">
                                      {strippedResumeJson.email}
                                    </dd>
                                  </div>
                                )}
                                {strippedResumeJson?.phone && (
                                  <div className="flex items-center gap-4 py-3">
                                    <Phone className="h-5 w-5 text-green-600" />
                                    <dt className="min-w-[70px] font-semibold text-gray-700">
                                      Phone
                                    </dt>
                                    <dd className="font-medium text-gray-900">
                                      {strippedResumeJson.phone}
                                    </dd>
                                  </div>
                                )}
                                {strippedResumeJson?.address && (
                                  <div className="flex items-center gap-4 py-3">
                                    <HiOutlineGlobeAlt className="h-5 w-5 text-purple-600" />
                                    <dt className="min-w-[70px] font-semibold text-gray-700">
                                      Location
                                    </dt>
                                    <dd className="font-medium text-gray-900">
                                      {strippedResumeJson.address}
                                    </dd>
                                  </div>
                                )}
                                {/* Render any additional personal info fields */}
                                {Object.entries(strippedResumeJson)
                                  .filter(
                                    ([key, value]) =>
                                      value &&
                                      ![
                                        "name",
                                        "email",
                                        "phone",
                                        "address",
                                        "skills",
                                        "experience",
                                        "education",
                                        "projects",
                                        "languages",
                                        "certifications",
                                      ].includes(key)
                                  )
                                  .map(([key, value]) => (
                                    <div
                                      key={key}
                                      className="flex items-center gap-4 py-3"
                                    >
                                      <dt className="min-w-[70px] font-semibold capitalize text-gray-700">
                                        {key.replace(/_/g, " ")}
                                      </dt>
                                      <dd className="font-medium text-gray-900">
                                        {Array.isArray(value)
                                          ? value.join(", ")
                                          : typeof value === "object"
                                          ? Object.entries(value)
                                              .map(([k, v]) => `${k}: ${v}`)
                                              .join(", ")
                                          : String(value)}
                                      </dd>
                                    </div>
                                  ))}
                              </dl>
                            </div>
                          </div>
                        </div>
                        <button
                          type="button"
                          className="absolute -bottom-6 left-1/2 -translate-x-1/2 rounded bg-brand-600 px-4 py-2 text-white shadow transition hover:bg-brand-700"
                          onClick={() => setDetailsBlur((prev) => !prev)}
                        >
                          {detailsBlur ? "Unlock Details" : "Hide Details"}
                        </button>
                      </div>
                    )}

                    {/* Skills */}
                    {strippedResumeJson?.skills &&
                      strippedResumeJson.skills.length > 0 && (
                        <div className="flex flex-col gap-3 rounded-2xl border border-gray-100 bg-white p-6 shadow">
                          <div className="mb-2 flex items-center gap-2">
                            <HiCollection className="h-5 w-5 text-blue-500" />
                            <h2 className="text-lg font-semibold">Skills</h2>
                          </div>
                          <div className="flex flex-wrap gap-2">
                            {strippedResumeJson.skills.map((skill, idx) => (
                              <span
                                key={idx}
                                className="inline-block rounded border border-blue-100 bg-blue-50 px-1.5 py-0.5 text-xs font-normal text-blue-700"
                              >
                                {skill}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                    {/* Experience */}
                    {strippedResumeJson?.experience &&
                      strippedResumeJson.experience.length > 0 && (
                        <div className="flex flex-col gap-3 rounded-2xl border border-gray-100 bg-white p-6 shadow">
                          <div className="mb-2 flex items-center gap-2">
                            <HiBriefcase className="h-5 w-5 text-amber-500" />
                            <h2 className="text-lg font-semibold">
                              Experience
                            </h2>
                          </div>
                          <div className="flex flex-col gap-4">
                            {strippedResumeJson.experience.map((exp, idx) => (
                              <div
                                key={idx}
                                className="border-b pb-2 last:border-b-0"
                              >
                                <div className="flex items-center gap-2">
                                  <span className="font-semibold">
                                    {exp.title || exp.position}
                                  </span>
                                  {exp.company && (
                                    <span className="text-gray-600">
                                      @ {exp.company}
                                    </span>
                                  )}
                                  {exp.duration && (
                                    <span className="ml-2 rounded bg-amber-50 px-2 py-0.5 text-xs text-amber-700">
                                      {exp.duration}
                                    </span>
                                  )}
                                </div>
                                {exp.description && (
                                  <div className="mt-1 text-sm text-gray-600">
                                    {exp.description}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                    {/* Education */}
                    {strippedResumeJson?.education &&
                      strippedResumeJson.education.length > 0 && (
                        <div className="flex flex-col gap-3 rounded-2xl border border-gray-100 bg-white p-6 shadow">
                          <div className="mb-2 flex items-center gap-2">
                            <HiAcademicCap className="h-5 w-5 text-green-600" />
                            <h2 className="text-lg font-semibold">Education</h2>
                          </div>
                          <div className="flex flex-col gap-4">
                            {strippedResumeJson.education.map((edu, idx) => (
                              <div
                                key={idx}
                                className="border-b pb-2 last:border-b-0"
                              >
                                <div className="flex items-center gap-2">
                                  <span className="font-semibold">
                                    {edu.degree}
                                  </span>
                                  {edu.institution && (
                                    <span className="text-gray-600">
                                      @ {edu.institution}
                                    </span>
                                  )}
                                  {edu.year && (
                                    <span className="ml-2 rounded bg-green-50 px-2 py-0.5 text-xs text-green-700">
                                      {edu.year}
                                    </span>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                    {/* Projects */}
                    {strippedResumeJson?.projects &&
                      strippedResumeJson.projects.length > 0 && (
                        <div className="flex flex-col gap-3 rounded-2xl border border-gray-100 bg-white p-6 shadow">
                          <div className="mb-2 flex items-center gap-2">
                            <HiOutlineDocumentText className="h-5 w-5 text-purple-600" />
                            <h2 className="text-lg font-semibold">Projects</h2>
                          </div>
                          <div className="flex flex-col gap-4">
                            {strippedResumeJson.projects.map((proj, idx) => (
                              <div
                                key={idx}
                                className="border-b pb-2 last:border-b-0"
                              >
                                <div className="flex items-center gap-2">
                                  <span className="font-semibold">
                                    {proj.name}
                                  </span>
                                  {proj.duration && (
                                    <span className="ml-2 rounded bg-purple-50 px-2 py-0.5 text-xs text-purple-700">
                                      {proj.duration}
                                    </span>
                                  )}
                                </div>
                                {proj.description && (
                                  <div className="mt-1 text-sm text-gray-600">
                                    {proj.description}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                    {/* Languages */}
                    {strippedResumeJson?.languages &&
                      strippedResumeJson.languages.length > 0 && (
                        <div className="flex flex-col gap-3 rounded-2xl border border-gray-100 bg-white p-6 shadow">
                          <div className="mb-2 flex items-center gap-2">
                            <HiOutlineGlobeAlt className="h-5 w-5 text-pink-500" />
                            <h2 className="text-lg font-semibold">Languages</h2>
                          </div>
                          <div className="flex flex-wrap gap-2">
                            {strippedResumeJson.languages.map((lang, idx) => (
                              <span
                                key={idx}
                                className="inline-block rounded border border-pink-100 bg-pink-50 px-1.5 py-0.5 text-xs font-normal text-pink-700"
                              >
                                {lang}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    {/* Certifications */}
                    {strippedResumeJson?.certifications &&
                      strippedResumeJson.certifications.length > 0 && (
                        <div className="flex flex-col gap-3 rounded-2xl border border-gray-100 bg-white p-6 shadow">
                          <div className="mb-2 flex items-center gap-2">
                            <HiBadgeCheck className="h-5 w-5 text-teal-600" />
                            <h2 className="text-lg font-semibold">
                              Certifications
                            </h2>
                          </div>
                          <div className="flex flex-col gap-4">
                            {strippedResumeJson.certifications.map(
                              (cert, idx) => (
                                <div
                                  key={idx}
                                  className="border-b pb-2 last:border-b-0"
                                >
                                  <div className="flex items-center gap-2">
                                    <span className="font-semibold">
                                      {cert.name}
                                    </span>
                                    {cert.issuer && (
                                      <span className="text-gray-600">
                                        by {cert.issuer}
                                      </span>
                                    )}
                                    {cert.year && (
                                      <span className="ml-2 rounded bg-teal-50 px-2 py-0.5 text-xs text-teal-700">
                                        {cert.year}
                                      </span>
                                    )}
                                  </div>
                                  {cert.description && (
                                    <div className="mt-1 text-sm text-gray-600">
                                      {cert.description}
                                    </div>
                                  )}
                                </div>
                              )
                            )}
                          </div>
                        </div>
                      )}
                    {/* If parsing failed or no fields, show a message */}
                    {!strippedResumeJson && (
                      <div className="text-yellow-600">
                        Resume data could not be parsed, but a resume was
                        uploaded.
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="col-span-2 text-red-500">
                    Resume details not available.
                  </div>
                )}
              </div>
            </div>
            {/* Right Column */}
            <div className="flex flex-col space-y-4">
              {Array.isArray(questionsAndAnswers) &&
                questionsAndAnswers.length > 0 && (
                  <div className="mb-4">
                    <h2 className="mb-4 text-xl font-bold text-green-700">
                      Interview Questions & Answers
                    </h2>
                    {/* Q&A Card with navigation */}
                    {/* Q&A Card with navigation (fixed hook order) */}
                    {(() => {
                      const qa = questionsAndAnswers[currentQAIndex];
                      if (!qa) return null;
                      return (
                        <div className="flex flex-col items-stretch">
                          <div
                            className="relative flex flex-col gap-6 overflow-y-auto rounded-2xl border border-gray-200 bg-gradient-to-br from-white to-gray-50 p-7 shadow-lg transition max-h-[50vh] md:max-h-[60vh] lg:max-h-[70vh]"
                            style={{ minHeight: 180 }}
                          >
                            {/* Top block: navigation + time frame */}
                            <div className="mb-4 flex items-center justify-between">
                              <div className="text-xs text-gray-400">
                                {currentQAIndex + 1} /{" "}
                                {questionsAndAnswers.length}
                              </div>
                              <div className="flex gap-2">
                                <button
                                  className={`flex items-center rounded bg-brand-600 px-2 py-0.5 text-xs text-white transition hover:bg-brand-700 disabled:cursor-not-allowed disabled:opacity-50`}
                                  onClick={() =>
                                    setCurrentQAIndex((i) => i - 1)
                                  }
                                  disabled={currentQAIndex === 0}
                                  aria-label="Previous Q&A"
                                  style={{ minWidth: 36 }}
                                >
                                  <svg
                                    width="20"
                                    height="20"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      d="M15 6l-6 6 6 6"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    />
                                  </svg>
                                </button>
                                <button
                                  className={`flex items-center rounded bg-brand-600 px-2 py-0.5 text-xs text-white transition hover:bg-brand-700 disabled:cursor-not-allowed disabled:opacity-50`}
                                  onClick={() =>
                                    setCurrentQAIndex((i) => i + 1)
                                  }
                                  disabled={
                                    currentQAIndex >=
                                    questionsAndAnswers.length - 1
                                  }
                                  aria-label="Next Q&A"
                                  style={{ minWidth: 36 }}
                                >
                                  <svg
                                    width="20"
                                    height="20"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      d="M9 6l6 6-6 6"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    />
                                  </svg>
                                </button>
                              </div>
                            </div>
                            {/* Q&A content below */}
                            <div className="flex flex-1 flex-col gap-4">
                              <div
                                className="cursor-pointer"
                                onClick={() => handleQuestionClick(qa)}
                                title={
                                  qa.startTimestamp
                                    ? `Jump to ${qa.startTimestamp}`
                                    : undefined
                                }
                              >
                                <div className="mb-3 flex items-start gap-3">
                                  <span className="pt-1 text-lg font-bold text-brand-700">
                                    Q:
                                  </span>
                                  <span className="flex-1 text-base font-semibold leading-relaxed text-gray-900">
                                    {qa.question}
                                  </span>
                                  {qa.startTimestamp && (
                                    <span className="ml-2 rounded bg-green-100 px-2 py-0.5 font-mono text-xs text-green-700">
                                      {(() => {
                                        let sec = 0;
                                        if (
                                          typeof qa.startTimestamp === "number"
                                        ) {
                                          sec = qa.startTimestamp;
                                        } else {
                                          const d = new Date(qa.startTimestamp);
                                          if (!isNaN(d.getTime())) {
                                            sec = Math.floor(
                                              (d.getTime() -
                                                new Date(
                                                  questionsAndAnswers[0].startTimestamp
                                                ).getTime()) /
                                                1000
                                            );
                                          }
                                        }
                                        const m = String(
                                          Math.floor(sec / 60)
                                        ).padStart(2, "0");
                                        const s = String(sec % 60).padStart(
                                          2,
                                          "0"
                                        );
                                        return `${m}:${s}`;
                                      })()}
                                    </span>
                                  )}
                                </div>
                                <div className="mt-2 flex items-start gap-3">
                                  <span className="pt-1 text-lg font-bold text-green-700">
                                    A:
                                  </span>
                                  <span className="flex-1 whitespace-pre-line rounded-xl border border-gray-100 bg-gray-50 px-4 py-3 text-base font-normal leading-relaxed text-gray-800 shadow-inner">
                                    {qa.answer}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })()}
                  </div>
                )}

              {/* Sidebar for large screens: always visible */}
              {!isSmallScreen && (
                <Sidebar profileData={profileData} profiles={profiles} />
              )}

              {/* Sidebar for small screens: modal with overlay */}
              {isSmallScreen && sidebarOpen && (
                <>
                  // Sidebar modal logic for small screens
                  {/* Overlay */}
                  <div
                    className="bg-black fixed inset-0 z-40 bg-opacity-40"
                    onClick={() => {
                      console.log("[Sidebar] Overlay clicked: closing sidebar");
                      setSidebarOpen(false);
                    }}
                    aria-label="Sidebar overlay"
                  />
                  {/* Sidebar panel */}
                  <div
                    className="fixed right-0 top-0 z-50 h-full w-80 max-w-full bg-white shadow-lg transition-transform duration-300"
                    onClick={(e) => {
                      console.log(
                        "[Sidebar] Clicked inside sidebar panel: stopping propagation"
                      );
                      e.stopPropagation();
                    }}
                    role="dialog"
                    aria-modal="true"
                  >
                    <div className="flex justify-end p-2">
                      <button
                        className="text-gray-500 hover:text-gray-800"
                        onClick={() => {
                          console.log(
                            "[Sidebar] Close button clicked: closing sidebar"
                          );
                          setSidebarOpen(false);
                        }}
                        aria-label="Close sidebar"
                      >
                        <span className="text-2xl">&times;</span>
                      </button>
                    </div>
                    <Sidebar profileData={profileData} profiles={profiles} />
                  </div>
                </>
              )}
            </div>
          </div>
          {/* Popup Modal */}
          {subPopup && (
            <div className="fixed inset-0 z-50 flex items-center justify-center">
              <div
                className="bg-black/20 absolute inset-0 backdrop-blur-sm"
                onClick={() => setSubPopup(false)}
              />
              <div className="relative z-50 w-full max-w-md rounded-lg bg-white shadow-xl">
                <div className="p-6">
                  <h2 className="mb-2 flex items-center text-2xl font-bold">
                    <AlertCircle className="mr-2 h-6 w-6 text-yellow-500" />
                    Unlock Profile Confirmation
                  </h2>
                  <p className="mb-4 text-gray-600">
                    Unlocking this profile will deduct 1 credit from your credit
                    balance.
                  </p>
                  <p className="mb-6 flex items-center">
                    Are you sure you want to proceed?
                  </p>
                  <div className="flex justify-end space-x-2">
                    <button
                      className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800"
                      onClick={() => setSubPopup(false)}
                    >
                      <X className="mr-1 h-4 w-4" />
                      Cancel
                    </button>
                    <button className="flex items-center rounded-md bg-brand-600 px-4 py-2 text-white hover:bg-brand-700">
                      <CreditCard className="mr-1 h-4 w-4" />
                      Unlock Now
                    </button>
                  </div>
                </div>
                <button
                  className="absolute right-2 top-2 text-gray-400 hover:text-gray-600"
                  onClick={() => setSubPopup(false)}
                >
                  <X className="h-6 w-6" />
                  <span className="sr-only">Close</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CandidateProfile;
