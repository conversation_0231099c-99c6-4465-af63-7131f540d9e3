import React from 'react';
/* eslint-disable */

import { HiCube, HiMenu, HiX } from "react-icons/hi";
import Links from "./components/Links";
import SidebarCard from "./components/SidebarCard";

// import SidebarCard from "components/sidebar/componentsrtl/SidebarCard";
import routes from "routes";
import { ArrowLeftToLine, Code, PanelLeftClose, X } from "lucide-react";
import LogoImage from 'assets/img/Visume-logo-icon.png';

const Sidebar = ({ open, onClose }) => {

  // Click-outside-to-close logic
  React.useEffect(() => {
    if (!open) return;
    function handleClickOutside(event) {
      const sidebar = document.getElementById("visume-sidebar");
      if (
        sidebar &&
        !sidebar.contains(event.target) &&
        window.innerWidth <= 768
      ) {
        onClose();
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [open, onClose]);

  return (
    <>
      {/* Mobile Backdrop */}
      {open && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 xl:hidden"
          onClick={onClose}
        />
      )}
      
      {/* Sidebar */}
      <div
        className={`fixed top-0 left-0 h-screen w-64 z-50 flex flex-col transition-transform duration-300 ease-in-out bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 shadow-lg ${
          open ? "translate-x-0" : "-translate-x-full"
        } xl:translate-x-0`}
      >
        {/* Sidebar Content */}
        <div className="relative h-full w-full flex flex-col">
        {/* Close button for mobile */}
        <button
          className="absolute top-4 right-4 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors xl:hidden"
          onClick={onClose}
        >
          <X className="w-4 h-4 text-gray-600 dark:text-gray-400" />
        </button>

        {/* Logo Section */}
        <div className="flex items-center gap-3 px-6 py-6 border-b border-gray-200 dark:border-gray-800">
          <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 shadow-sm">
            <img src={LogoImage} alt="Visume logo" className="w-5 h-5" />
          </div>
          <div className="flex flex-col">
            <span className="text-lg font-semibold text-gray-900 dark:text-white leading-tight">
              Visume.ai
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              AI-Powered Resumes
            </span>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-3 py-4 space-y-1">
          <Links routes={routes} onClose={onClose}/>
        </nav>

        {/* Bottom Card */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-800">
          <SidebarCard />
        </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
