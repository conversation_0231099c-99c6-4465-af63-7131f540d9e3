const {
  getUploadUrlFromCacheOrNew,
  generateScores,
  generateVideoRandomId,
  generateSingleQuestion,
} = require("../utils/helpers");
const pool = require("../config/db");

// Video Profile Creation API
exports.createVideoProfile = async (req, res) => {
  const { candId, jobRole, skills, companyType, experience, salary } = req.body;

  // Validate the required fields
  if (
    !candId ||
    !jobRole ||
    !skills ||
    !companyType ||
    !experience ||
    !salary
  ) {
    return res.status(400).json({
      message: "All fields are required",
      details: "Please provide all required fields: candId, jobRole, skills, companyType, experience, and salary"
    });
  }

  // Validate company type
  if (!['startup', 'mid_range', 'mnc'].includes(companyType.toLowerCase())) {
    return res.status(400).json({
      message: "Invalid company type",
      details: "Company type must be 'startup', 'mid_range', or 'mnc'"
    });
  }

  // Normalize company type to lowercase
  const normalizedCompanyType = companyType.toLowerCase();

  try {
    // Process the skills field
    const skillsString = skills.join(", ");

    // Get a connection from the pool
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res.status(500).send("Failed to connect to the database.");
      }

      // Check if the same role and skills already exist for the same candidate
      const checkQuery = `
        SELECT * FROM videoprofile
        WHERE cand_id = ? AND role = ? AND skills = ?
      `;

      connection.query(
        checkQuery,
        [candId, jobRole, skillsString],
        (err, results) => {
          if (err) {
            console.error("Error checking existing video profile:", err);
            connection.release(); // Release the connection
            return res.status(500).json({
              message: "Failed to check existing video profile.",
            });
          }

          if (results.length > 0) {
            connection.release(); // Release the connection
            return res.status(400).json({
              message:
                "A video profile with the same role and skills already exists.",
            });
          } else {
            // Generate a random ID for the video profile
            const videoProfileId = generateVideoRandomId();

            // Generate just the first question with proper formatting
            const generateFirstQuestion = async () => {
              try {
                const generatedQuestion = await generateSingleQuestion(
                  jobRole,
                  [], // No previous Q&A for first question
                  skills,
                  true, // isFirstQuestion
                  normalizedCompanyType,
                  experience
                );

                const firstQuestion = {
                  questionId: 1, // Assign a unique ID, starting with 1 for the first question
                  ...generatedQuestion,
                  startTimestamp: null,
                  endTimestamp: null,
                  answer: null,
                  scores: {
                    communication: 0,
                    technical: 0,
                    overall: 0
                  },
                  follow_up: generatedQuestion.follow_up || "Please provide specific examples from your experience"
                };

                return [firstQuestion]; // Return array with single question
              } catch (error) {
                console.error("Error generating first question:", error);
                throw error;
              }
            };

            // Generate first question and proceed with profile creation
            generateFirstQuestion()
              .then((questions) => {

                // Prepare the video profile data for insertion into the database
                const videoProfileData = {
                  videoProfileId,
                  candId,
                  jobRole,
                  skills: skills,
                  companyType,
                  experience,
                  salary,
                  questions: questions, // Use the generated first question
                  videoUrl: null,
                  score: null,
                  status: "started",
                };

                // Prepare the SQL query
                const sqlQuery = `
                INSERT INTO videoprofile (
                  video_profile_id,
                  cand_id,
                  role,
                  skills,
                  job_type,
                  experience_range,
                  salary,
                  questions,
                  video_url,
                  score,
                  status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
              `;

                // Properly stringify questions array for database storage
                const questionsJson = JSON.stringify(videoProfileData.questions);

                console.log("Storing questions in database:", {
                  raw: videoProfileData.questions,
                  json: questionsJson,
                  count: videoProfileData.questions.length
                });

                // Execute the query to insert the video profile data into the database
                connection.query(
                  sqlQuery,
                  [
                    videoProfileId,
                    candId,
                    jobRole,
                    skillsString,
                    normalizedCompanyType,
                    experience,
                    JSON.stringify(salary),
                    questionsJson,
                    null,
                    null,
                    "started",
                  ],
                  (error, results) => {
                    connection.release(); // Release the connection

                    if (error) {
                      console.error(
                        "Error inserting video profile into database:",
                        error
                      );
                      return res.status(500).json({
                        message: "Failed to create video profile.",
                      });
                    }

                    // Send back the response with the created video profile data
                    res.json({
                      videoProfileId,
                      candId,
                      jobRole,
                      skills: skills,
                      companyType,
                      experience,
                      salary,
                      questions,
                    });
                  }
                );
              })
              .catch((error) => {
                connection.release(); // Release the connection
                console.error("Error generating questions:", error);
                res.status(500).json({
                  message: "Failed to generate interview questions.",
                });
              });
          }
        }
      );
    });
  } catch (error) {
    console.error("Error creating video profile:", error);
    res.status(500).send({
      message: "Failed to create video profile.",
    });
  }
};


// Generate Score Creation API
exports.generateScore = async (req, res) => {
  const { questions } = req.body;
  
  // Validate the required fields
  if (!questions || !Array.isArray(questions) || questions.length === 0) {
    return res.status(400).json({
      message: "Questions must be a non-empty array of questions and answers."
    });
  }

  // Validate each question has required structure
  const isValidStructure = questions.every(q => q.question && q.answer);
  if (!isValidStructure) {
    return res.status(400).json({
      message: "Each question must have 'question' and 'answer' fields."
    });
  }

  try {
    // Generate scores based on the interview questions
    const scores = await generateScores(questions);
    
    // Always return a 200 response since generateScores now handles failures gracefully
    res.json(scores);
    
  } catch (error) {
    console.error("Error in score generation:", error);
    
    res.status(500).json({
      message: "Failed to generate report due to a technical issue. Please contact support.",
      error: error.message
    });
  }
};

// Finish Video Profile API
exports.addVideoProfileData = async (req, res) => {
  const { videoProfileId, videoUrl, score, questions } = req.body;

  // Validate the required fields
  if (!videoProfileId || !videoUrl || !score || !questions) {
    return res.status(400).send("All fields are required.");
  }

  try {
    // Get a connection from the pool
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res.status(500).send("Failed to connect to the database.");
      }

      // Check if the video profile exists
      const checkQuery = `
        SELECT * FROM videoprofile
        WHERE video_profile_id = ?
      `;

      connection.query(checkQuery, [videoProfileId], (err, results) => {
        if (err) {
          console.error("Error checking video profile:", err);
          connection.release(); // Always release the connection
          return res.status(500).send("Failed to check video profile.");
        }

        if (results.length === 0) {
          connection.release(); // Release the connection
          return res.status(404).send("Video profile not found.");
        } else {
          // Convert questions array to JSON string
          let questionsString;
          try {
            questionsString = JSON.stringify(questions);
          } catch (jsonError) {
            console.error("Error stringifying questions:", jsonError);
            connection.release();
            return res.status(400).send("Invalid questions format.");
          }
          // Convert questions array to JSON string
          let scoreString;
          try {
            scoreString = JSON.stringify(score);
          } catch (jsonError) {
            console.error("Error stringifying scores:", jsonError);
            connection.release();
            return res.status(400).send("Invalid scores format.");
          }

          // Access the Overall_Score from the score object
          const overallScore = parseFloat(score?.score?.Overall_Score) || 0; // Default to 0 if undefined or not a number

          // Update the video profile with the video URL and score
          const updateQuery = `
            UPDATE videoprofile
            SET video_url = ?, score = ?, status = ?, questions = ?
            WHERE video_profile_id = ?
          `;

          connection.query(
            updateQuery,
            [
              videoUrl,
              scoreString,
              "notSubmitted",
              questionsString,
              videoProfileId,
            ],
            (error, updateResults) => {
              connection.release(); // Always release the connection

              if (error) {
                console.error("Error updating video profile:", error);
                return res.status(500).send("Failed to finish video profile.");
              }

              // Send success response
              res.json({
                message: "Video profile updated successfully.",
                videoProfileId,
                videoUrl,
                score,
                questions,
                status: "notSubmitted",
              });
            }
          );
        }
      });
    });
  } catch (error) {
    console.error("Error finishing video profile:", error);
    res
      .status(500)
      .json({ message: "Failed to finish the video profile.", error });
  }
};

// Complete Video Profile API
exports.finishVideoProfile = async (req, res) => {
  const { score, videoProfileId } = req.body;
  // Validate the required fields
  if (!videoProfileId || !score.toString()) {
    return res.status(400).json({ msg: "All fields are required." });
  }
  if (score < 5) {
    return res
      .status(429)
      .json({ msg: "Score is too low , Video Resume Can't be Submitted." });
  }
  try {
    // Get a connection from the pool
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res.status(500).send("Failed to connect to the database.");
      }

      // Check if the video profile exists
      const checkQuery = `
        SELECT * FROM videoprofile
        WHERE video_profile_id = ?
      `;

      connection.query(checkQuery, [videoProfileId], (err, results) => {
        if (err) {
          console.error("Error checking video profile:", err);
          connection.release(); // Always release the connection
          return res.status(500).send("Failed to check video profile.");
        }

        if (results.length === 0) {
          connection.release(); // Release the connection
          return res.status(404).send("Video profile not found.");
        } else {
          // Update the video profile with the video URL and score
          const updateQuery = `
            UPDATE videoprofile
            SET status = ?
            WHERE video_profile_id = ?
          `;

          connection.query(
            updateQuery,
            [score > 5 ? "Active" : "InActive", videoProfileId],
            (error, updateResults) => {
              connection.release(); // Always release the connection

              if (error) {
                console.error("Error updating video profile:", error);
                return res.status(500).send("Failed to finish video profile.");
              }

              // Send success response
              res.json({
                message: "Video profile finished successfully.",
                status: score > 5 ? "Active" : "InActive",
              });
            }
          );
        }
      });
    });
  } catch (error) {
    console.error("Error finishing video profile:", error);
    res
      .status(500)
      .json({ message: "Failed to finish the video profile.", error });
  }
};

// Fetch all video profiles
exports.listVideoProfiles = (req, res) => {
  try {
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res.status(500).send("Failed to connect to the database.");
      }

      // SQL query to select all video profiles
      const sqlQuery = `SELECT * FROM videoprofile`;

      connection.query(sqlQuery, (error, results) => {
        connection.release(); // Release the connection

        if (error) {
          console.error("Error fetching video profiles from database:", error);
          return res.status(500).send("Failed to retrieve video profiles.");
        }

        // Send back the retrieved video profiles
        res.json(results);
      });
    });
  } catch (error) {
    console.error("Error listing video profiles:", error);
    res.status(500).send("Failed to list video profiles.");
  }
};

// Fetch video profiles by candidate ID
exports.listVideoProfilesByCandidateId = (req, res) => {
  const { cand_id } = req.params;

  try {
    // Get a connection from the pool
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res.status(500).send("Failed to connect to the database.");
      }

      // SQL query to select video profiles by candidate ID
      const sqlQuery = `SELECT * FROM videoprofile WHERE cand_id = ?`;

      connection.query(sqlQuery, [cand_id], (error, results) => {
        connection.release(); // Release the connection

        if (error) {
          console.error("Error fetching video profiles from database:", error);
          return res.status(500).send("Failed to retrieve video profiles.");
        }

        // Send back the retrieved video profiles
        res.json(results);
      });
    });
  } catch (error) {
    console.error("Error listing video profiles by candidate ID:", error);
    res.status(500).send("Failed to list video profiles.");
  }
};

// Update video profile questions
exports.updateVideoProfileQuestions = async (req, res) => {
  const { vpid } = req.params;
  const { questionId, updatedQuestion } = req.body;

  console.log("Received request to updateVideoProfileQuestions:");
  console.log("req.params:", req.params);
  console.log("req.body:", req.body);

  // Validate the required fields
  if (!vpid) {
    return res.status(400).json({
      message: "Video profile ID is required"
    });
  }

  if (!updatedQuestion) {
    return res.status(400).json({
      message: "updatedQuestion is required"
    });
  }

  try {
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res.status(500).json({
          message: "Failed to connect to the database"
        });
      }

      // Check if video profile exists
      const checkQuery = `
        SELECT questions FROM videoprofile
        WHERE video_profile_id = ?
      `;

      connection.query(checkQuery, [vpid], (err, results) => {
        if (err) {
          console.error("Error checking video profile:", err);
          connection.release();
          return res.status(500).json({
            message: "Failed to check video profile"
          });
        }

        if (results.length === 0) {
          connection.release();
          return res.status(404).json({
            message: "Video profile not found"
          });
        }

        const existingQuestions = JSON.parse(results[0].questions || "[]");
        let questionFound = false;

        let newQuestions;
        if (questionId) {
          // Attempt to update an existing question if questionId is provided
          newQuestions = existingQuestions.map((q) => {
            if (Number(q.questionNumber) === Number(questionId)) {
              questionFound = true;
              return { ...q, ...updatedQuestion };
            }
            return q;
          });
          if (!questionFound) {
            connection.release();
            return res.status(404).json({
              message: "Question not found in video profile for update"
            });
          }
        } else {
          // If no questionId is provided, append the new question
          newQuestions = [...existingQuestions, updatedQuestion];
          questionFound = true; // Consider it found as we are appending
        }

        if (!questionFound) {
          connection.release();
          return res.status(404).json({
            message: "Question not found in video profile"
          });
        }

        const updateQuery = `
          UPDATE videoprofile
          SET questions = ?
          WHERE video_profile_id = ?
        `;

        connection.query(updateQuery, [JSON.stringify(newQuestions), vpid], (error, updateResults) => {
          connection.release();

          if (error) {
            console.error("Error updating video profile questions:", error);
            return res.status(500).json({
              message: "Failed to update video profile questions"
            });
          }

          res.json({
            message: "Question updated successfully",
            videoProfileId: vpid,
            questions: newQuestions
          });
        });
      });
    });
  } catch (error) {
    console.error("Error updating video profile questions:", error);
    res.status(500).json({
      message: "Failed to update video profile questions",
      error: error.message
    });
  }
};

// Fetch candidate profile Details and video resume by candidate ID
exports.listCandidateAndVideoProfilesByCandidateId = (req, res) => {
  const { cand_id } = req.params;

  try {
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res.status(500).send("Failed to connect to the database.");
      }

      const candidateQuery = `SELECT * FROM jobseeker WHERE cand_id = ?`;
      
      connection.query(candidateQuery, [cand_id], (error, candidateResults) => {
        if (error) {
          connection.release();
          console.error("Error fetching candidate profiles:", error);
          return res.status(500).send("Failed to retrieve candidate profiles.");
        }

        if (!candidateResults || candidateResults.length === 0) {
          connection.release();
          return res.status(404).json({ message: "Candidate not found" });
        }

        // Ensure stripped_resume is valid JSON or provide default value
        let parsedResume = null;
        if (candidateResults[0].stripped_resume) {
          try {
            parsedResume = JSON.parse(candidateResults[0].stripped_resume);
          } catch (e) {
            console.error("Error parsing stripped_resume:", e);
          }
        }

        // Update candidate result with parsed resume
        candidateResults[0].stripped_resume = parsedResume;
        if (error) {
          connection.release();
          console.error("Error fetching candidate profiles:", error);
          return res.status(500).send("Failed to retrieve candidate profiles.");
        }

        // Query to select video profiles by candidate ID
        const videoQuery = `SELECT * FROM videoprofile WHERE cand_id = ?`;

        connection.query(videoQuery, [cand_id], async (error, videoResults) => {
          if (error) {
            connection.release();
            console.error("Error fetching video profiles:", error);
            return res.status(500).send("Failed to retrieve video profiles.");
          }

          // Initialize counters for interactions and status
          let interactionCount = { click: 0, view: 0 };
          let statusCount = { shortlisted: 0, unlocked: 0 };

          // Helper function to fetch analytics and employer profile data for each video profile
          const fetchAdditionalData = async (videoProfileId) => {
            // Query to count interaction types from analytics table
            const interactionQuery = `
              SELECT interaction_type, COUNT(*) AS count 
              FROM analytics 
              WHERE profile_id = ? 
              GROUP BY interaction_type
            `;

            // Query to count status types from employerprofiles table
            const statusQuery = `
              SELECT status, COUNT(*) AS count 
              FROM employerprofiles 
              WHERE video_resume_id = ? 
              GROUP BY status
            `;

            // Promisified database query function
            const queryPromise = (query, params) =>
              new Promise((resolve, reject) => {
                connection.query(query, params, (err, results) => {
                  if (err) reject(err);
                  else resolve(results);
                });
              });

            try {
              // Fetch interaction data
              const interactionData = await queryPromise(interactionQuery, [
                videoProfileId,
              ]);
              interactionData.forEach((row) => {
                interactionCount[row.interaction_type] += row.count;
              });

              // Fetch status data
              const statusData = await queryPromise(statusQuery, [
                videoProfileId,
              ]);
              statusData.forEach((row) => {
                statusCount[row.status] += row.count;
              });
            } catch (err) {
              console.error("Error fetching additional data:", err);
              throw err;
            }
          };

          // Fetch additional data for each video profile
          try {
            await Promise.all(
              videoResults.map((video) => fetchAdditionalData(video.id))
            );
            connection.release();

            // Send response with candidate profile, video profiles, and aggregated counts
            res.json({
              candidateProfile: [
                candidateResults[0]
                  ? {
                      ...candidateResults[0],
                      interactions: interactionCount,
                      statusCounts: statusCount,
                    }
                  : {},
              ],
              videoProfiles: videoResults,
            });
          } catch (fetchError) {
            connection.release();
            res
              .status(500)
              .send("Failed to fetch interaction and status data.");
          }
        });
      });
    });
  } catch (error) {
    console.error("Error listing candidate and video profiles:", error);
    res.status(500).send("Failed to list candidate and video profiles.");
  }
};

// Core Filteration Helper Function
exports.helperFilterCandidates = async ({
  preferred_location,
  role,
  selectedSkills = [],
  experience,
  expected_salary,
  current_salary,
  score,
}) => {
  return new Promise((resolve, reject) => {
    
    // Get a connection from the pool
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return reject("Failed to connect to the database.");
      }

      // Initialize the jobseeker query based on filters
      let candidateSql = `SELECT * FROM jobseeker WHERE 1=1`;
      const candidateQueryParams = [];

      if (preferred_location) {
        candidateSql += ` AND LOWER(preferred_location) = LOWER(?)`;
        candidateQueryParams.push(preferred_location);
      }

      connection.query(
        candidateSql,
        candidateQueryParams,
        (error, candidateResults) => {
          if (error) {
            connection.release();
            console.error("Error fetching candidates:", error);
            return reject("Failed to retrieve candidate profiles.");
          }

          if (candidateResults.length === 0) {
            connection.release();
            return resolve([]); // No candidates found
          }

          const candidateIds = candidateResults.map((cand) => cand.cand_id);

          // Build video profile query
          let videoSql = `
          SELECT vp.* FROM videoprofile vp WHERE vp.cand_id IN (?)`;
          if (role) videoSql += ` AND LOWER(vp.role) = LOWER(?)`;
          if (experience) videoSql += ` AND vp.experience_range = ?`;
          if (score)
            videoSql += ` AND JSON_UNQUOTE(JSON_EXTRACT(vp.score, '$.score.Overall_Score')) > ?`;
          if (expected_salary)
            videoSql += ` AND JSON_UNQUOTE(JSON_EXTRACT(vp.salary, '$.expected')) = ?`;
          if (current_salary)
            videoSql += ` AND JSON_UNQUOTE(JSON_EXTRACT(vp.salary, '$.current')) = ?`;
          if (selectedSkills.length > 0) {
            const skillConditions = selectedSkills
              .map(() => `LOWER(vp.skills) LIKE ?`)
              .join(" AND ");
            videoSql += ` AND (${skillConditions})`;
          }

          const videoQueryParams = [candidateIds];
          if (role) videoQueryParams.push(role.toLowerCase());
          if (experience) videoQueryParams.push(experience);
          if (score) videoQueryParams.push(score);
          if (expected_salary) videoQueryParams.push(expected_salary);
          if (current_salary) videoQueryParams.push(current_salary);
          videoQueryParams.push(
            ...selectedSkills.map((skill) => `%${skill.toLowerCase()}%`)
          );

          connection.query(
            videoSql,
            videoQueryParams,
            (error, videoResults) => {
              connection.release();

              if (error) {
                console.error("Error fetching video profiles:", error);
                return reject("Failed to retrieve video profiles.");
              }

              const mergedResults = videoResults.map((video) => {
                const candidateDetail = candidateResults.find(
                  (cand) => cand.cand_id === video.cand_id
                );
                return {
                  ...video,
                  candidateDetails: candidateDetail
                    ? [{
                        profile_picture: candidateDetail.profile_picture,
                        cand_name: candidateDetail.cand_name,
                        preferred_location: candidateDetail.preferred_location,
                      }]
                    : [null],
                };
              });

              resolve(mergedResults);
            }
          );
        }
      );
    });
  });
};

// Filter candidates profiles
exports.filterCandidate = async (req, res) => {
  try {
    const {
      preferred_location,
      role,
      selectedSkills,
      experience,
      expected_salary,
      current_salary,
      score,
    } = req.query;

    const parsedSkills = selectedSkills ? JSON.parse(selectedSkills) : [];
    const candidates = await this.helperFilterCandidates({
      preferred_location,
      role,
      selectedSkills: parsedSkills,
      experience,
      expected_salary,
      current_salary,
      score,
    });

    return res.json({ candidateProfiles: candidates });
  } catch (error) {
    console.error("Error filtering candidates:", error);
    res.status(500).send("Failed to filter candidate profiles.");
  }
};

// Fetch All candidates profiles with pagination
exports.listAllCandidates = (req, res) => {
  try {
    // Get a connection from the pool
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res.status(500).send("Failed to connect to the database.");
      }

      // Get pagination parameters from the query
      const page = parseInt(req.query.page) || 1; // Default to page 1
      const pageSize = parseInt(req.query.pageSize) || 10; // Default to 10 items per page
      const offset = (page - 1) * pageSize;

      // SQL query to select video profiles with pagination
      const videoProfileQuery = `SELECT * FROM videoprofile LIMIT ? OFFSET ?`;

      connection.query(
        videoProfileQuery,
        [pageSize, offset],
        (error, candidateResults) => {
          if (error) {
            connection.release(); // Release the connection in case of error
            console.error(
              "Error fetching candidate profiles from database:",
              error
            );
            return res
              .status(500)
              .send("Failed to retrieve candidate profiles.");
          }

          // Fetch the total number of video candidates for pagination info
          connection.query(
            "SELECT COUNT(*) as total FROM videoprofile", // Changed to videoprofile
            (countError, countResults) => {
              connection.release(); // Release the connection after both queries
              if (countError) {
                console.error(
                  "Error fetching total candidate count:",
                  countError
                );
                return res
                  .status(500)
                  .send("Failed to retrieve candidate count.");
              }

              const totalCandidates = countResults[0].total;
              const totalPages = Math.ceil(totalCandidates / pageSize);

              // Optionally, fetch detailed candidate information if needed
              const candidateDetailsQueries = candidateResults.map(
                (candidate) => {
                  return new Promise((resolve, reject) => {
                    const candidateDetailQuery = `SELECT * FROM jobseeker WHERE cand_id = ?`;
                    connection.query(
                      candidateDetailQuery,
                      [candidate.cand_id],
                      (detailError, detailResults) => {
                        if (detailError) {
                          return reject(detailError);
                        }
                        resolve({
                          ...candidate,
                          candidateDetails: [
                            {
                              profile_picture: detailResults[0].profile_picture,
                              cand_name: detailResults[0].cand_name,
                              preferred_location:
                                detailResults[0].preferred_location,
                            },
                          ],
                        });
                      }
                    );
                  });
                }
              );

              Promise.all(candidateDetailsQueries)
                .then((detailedCandidates) => {
                  // Send back both candidate details and pagination info in a single JSON response
                  res.json({
                    candidateProfiles: detailedCandidates,
                    pagination: {
                      currentPage: page,
                      pageSize: pageSize,
                      totalCandidates: totalCandidates,
                      totalPages: totalPages,
                    },
                  });
                })
                .catch((detailError) => {
                  console.error(
                    "Error fetching candidate details:",
                    detailError
                  );
                  res.status(500).send("Failed to retrieve candidate details.");
                });
            }
          );
        }
      );
    });
  } catch (error) {
    console.error("Error listing candidate and video profiles:", error);
    res.status(500).send("Failed to list candidate and video profiles.");
  }
};

// Fetch All candidates profiles with pagination
exports.listSuggestedCandidates = (req, res) => {
  try {
    const emp_id = req.query.emp_id;

    // Get a connection from the pool
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res.status(500).send("Failed to connect to the database.");
      }

      // SQL query to select 10 random video profiles not shortlisted by the emp_id
      const videoProfileQuery = `
        SELECT vp.* 
        FROM videoprofile vp
        LEFT JOIN employerprofiles ep ON vp.id = ep.video_resume_id AND ep.emp_id = ?
        WHERE ep.emp_id IS NULL
        ORDER BY RAND()
        LIMIT 10
      `;

      connection.query(
        videoProfileQuery,
        [emp_id],
        (error, candidateResults) => {
          if (error) {
            connection.release(); // Release the connection in case of error
            console.error(
              "Error fetching candidate profiles from database:",
              error
            );
            return res
              .status(500)
              .send("Failed to retrieve candidate profiles.");
          }

          // Optionally, fetch detailed candidate information if needed
          const candidateDetailsQueries = candidateResults.map((candidate) => {
            return new Promise((resolve, reject) => {
              const candidateDetailQuery = `SELECT * FROM jobseeker WHERE cand_id = ?`;
              connection.query(
                candidateDetailQuery,
                [candidate.cand_id],
                (detailError, detailResults) => {
                  if (detailError) {
                    return reject(detailError);
                  }
                  resolve({
                    ...candidate,
                    candidateDetails: [
                      {
                        profile_picture: detailResults[0].profile_picture,
                        cand_name: detailResults[0].cand_name,
                        preferred_location: detailResults[0].preferred_location,
                      },
                    ],
                  });
                }
              );
            });
          });

          Promise.all(candidateDetailsQueries)
            .then((detailedCandidates) => {
              // Send back candidate details in a single JSON response
              res.json({
                candidateProfiles: detailedCandidates,
              });
            })
            .catch((detailError) => {
              console.error("Error fetching candidate details:", detailError);
              res.status(500).send("Failed to retrieve candidate details.");
            });
        }
      );
    });
  } catch (error) {
    console.error("Error listing candidate and video profiles:", error);
    res.status(500).send("Failed to list candidate and video profiles.");
  }
};

// Delete a video profile by video profile ID
exports.deleteVideoProfileById = async (req, res) => {
  const { video_profile_id } = req.params;

  try {
    // Get a connection from the pool
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res
          .status(500)
          .json({ message: "Failed to connect to the database." });
      }

      // SQL query to check if the video profile exists for the provided video profile ID
      const checkQuery = `
        SELECT * FROM videoprofile WHERE video_profile_id = ?;
      `;

      connection.query(checkQuery, [video_profile_id], (err, results) => {
        if (err) {
          connection.release(); // Release the connection
          console.error("Error checking video profile:", err);
          return res
            .status(500)
            .json({ message: "Failed to check video profile." });
        }

        if (results.length === 0) {
          connection.release(); // Release the connection
          return res.status(404).json({
            message: "No video profile found for this video profile ID.",
          });
        }

        // Video profile exists, proceed to delete it
        const deleteQuery = `
          DELETE FROM videoprofile WHERE video_profile_id = ?;
        `;

        connection.query(deleteQuery, [video_profile_id], (err) => {
          connection.release(); // Release the connection

          if (err) {
            console.error("Error deleting video profile:", err);
            return res
              .status(500)
              .json({ message: "Failed to delete the video profile." });
          }

          // Successfully deleted the video profile
          res.status(200).json({
            message: "Video profile deleted successfully for video profile ID.",
            data: {
              video_profile_id,
            },
          });
        });
      });
    });
  } catch (error) {
    console.error("Error deleting video profile by video profile ID:", error);
    res.status(500).json({ message: "Failed to delete the video profile." });
  }
};

// Fetch a video profile by video profile ID
exports.fetchVideoProfileById = async (req, res) => {
  // Accept both :vpid and :video_profile_id for flexibility
  const video_profile_id = req.params.vpid || req.params.video_profile_id;
  try {
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res
          .status(500)
          .json({ message: "Failed to connect to the database." });
      }

      const checkQuery = `
        SELECT * FROM videoprofile WHERE video_profile_id = ?;
      `;

      connection.query(checkQuery, [video_profile_id], (err, results) => {
        connection.release();

        if (err) {
          console.error("Error fetching video profile:", err);
          return res
            .status(500)
            .json({ message: "Failed to fetch video profile." });
        }

        if (results.length === 0) {
          return res.status(404).json({
            message: "No video profile found for this video profile ID.",
          });
        }

        // Always include questionsAndAnswers array, mapping DB questions field
        const profile = results[0];
        let questionsArr = [];
        try {
          if (profile.questions) {
            if (typeof profile.questions === "string") {
              questionsArr = JSON.parse(profile.questions);
            } else if (Array.isArray(profile.questions)) {
              questionsArr = profile.questions;
            }
          }
        } catch (e) {
          questionsArr = [];
        }
        // Guarantee array of question objects with all fields (including timestamp)
        const questionsAndAnswers = Array.isArray(questionsArr)
          ? questionsArr.map(q => ({ ...q }))
          : [];

        res.status(200).json({
          message: "Video profile fetched successfully.",
          data: profile,
          questionsAndAnswers,
        });
      });
    });
  } catch (error) {
    console.error("Error fetching video profile by video profile ID:", error);
    res.status(500).json({ message: "Failed to fetch the video profile." });
  }
};

// Add Video Profile Analytics API
exports.addAnalytics = async (req, res) => {
  const { employer_id, profile_id, interaction_type } = req.body;

  // Validate required fields
  if (!interaction_type || !employer_id || !profile_id) {
    return res
      .status(400)
      .json({ message: "Interaction Type, Employer ID, and Profile ID are required." });
  }

  // Validate interaction_type value
  if (!["view", "click"].includes(interaction_type)) {
    return res
      .status(400)
      .json({ message: "Invalid interaction type. Allowed values are 'view' and 'click'." });
  }

  try {
    // Get a connection from the pool
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res.status(500).json({ message: "Failed to connect to the database." });
      }

      // Check if employer_id exists
      const checkEmployerQuery = `SELECT id FROM employer WHERE id = ?`;
      connection.query(
        checkEmployerQuery,
        [employer_id],
        (err, employerResults) => {
          if (err) {
            connection.release();
            console.error("Error checking employer ID:", err);
            return res.status(500).json({ message: "Error checking employer ID." });
          }

          if (employerResults.length === 0) {
            connection.release();
            return res.status(400).json({ message: "Invalid employer ID." });
          }

          // Check if profile_id exists
          const checkProfileQuery = `SELECT id FROM videoprofile WHERE id = ?`;
          connection.query(
            checkProfileQuery,
            [profile_id],
            (err, profileResults) => {
              if (err) {
                connection.release();
                console.error("Error checking profile ID:", err);
                return res.status(500).json({ message: "Error checking profile ID." });
              }

              if (profileResults.length === 0) {
                connection.release();
                return res.status(400).json({ message: "Invalid profile ID." });
              }

              // Allow duplicate interactions if interaction_type is 'click'
              if (interaction_type === "view") {
                // Check if an identical interaction already exists for 'view'
                const checkDuplicateQuery = `
                  SELECT * FROM analytics 
                  WHERE employer_id = ? AND profile_id = ? AND interaction_type = ?
                `;
                connection.query(
                  checkDuplicateQuery,
                  [employer_id, profile_id, interaction_type],
                  (err, duplicateResults) => {
                    if (err) {
                      connection.release();
                      console.error(
                        "Error checking duplicate interaction:",
                        err
                      );
                      return res
                        .status(500)
                        .json({ message: "Error checking duplicate interaction." });
                    }

                    if (duplicateResults.length > 0) {
                      connection.release();
                      return res
                        .status(400)
                        .json({
                          message: "Duplicate interaction: the same interaction type already exists for this employer and profile."
                        });
                    }

                    // Insert into analytics if no duplicate is found
                    const createAnalyticsQuery = `
                      INSERT INTO analytics (employer_id, profile_id, interaction_type, timestamp)
                      VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                    `;
                    connection.query(
                      createAnalyticsQuery,
                      [employer_id, profile_id, interaction_type],
                      (err, result) => {
                        connection.release();
                        if (err) {
                          console.error("Error inserting analytics data:", err);
                          return res
                            .status(500)
                            .json({ message: "Error inserting analytics data." });
                        }

                        res.status(201).json({
                          message: "Analytics data added successfully.",
                          data: result.insertId,
                        });
                      }
                    );
                  }
                );
              } else {
                // Insert for 'click' without checking duplicates
                const createAnalyticsQuery = `
                  INSERT INTO analytics (employer_id, profile_id, interaction_type, timestamp)
                  VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                `;
                connection.query(
                  createAnalyticsQuery,
                  [employer_id, profile_id, interaction_type],
                  (err, result) => {
                    connection.release();
                    if (err) {
                      console.error("Error inserting analytics data:", err);
                      return res
                        .status(500)
                        .json({ message: "Error inserting analytics data." });
                    }

                    res.status(201).json({
                      message: "Analytics data added successfully.",
                      data: result.insertId,
                    });
                  }
                );
              }
            }
          );
        }
      );
    });
  } catch (error) {
    console.error("Error finishing video profile:", error);
    res
      .status(500)
      .json({ message: "Failed to finish the video profile.", error });
  }
};
