import { useState, useEffect, useCallback } from 'react';

const useEmailValidation = () => {
  const [emailValidation, setEmailValidation] = useState({
    isValid: null,
    message: "",
    isChecking: false
  });

  const [debounceTimer, setDebounceTimer] = useState(null);

  // Email format validation
  const validateEmailFormat = (email) => {
    if (!email) return { isValid: null, message: "" };
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValidFormat = emailRegex.test(email);
    
    return {
      isValid: isValidFormat,
      message: isValidFormat ? "" : "Invalid email format"
    };
  };

  // Check email uniqueness with backend
  const checkEmailUniqueness = async (email) => {
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/check-email`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ email }),
        }
      );

      const data = await response.json();

      if (response.ok) {
        return {
          isValid: true,
          message: "Email is available",
          available: true
        };
      } else if (response.status === 409) {
        return {
          isValid: false,
          message: data.message || "Email already exists. Please log in.",
          available: false
        };
      } else {
        return {
          isValid: false,
          message: "Error checking email availability",
          available: false
        };
      }
    } catch (error) {
      console.error("Email validation error:", error);
      return {
        isValid: false,
        message: "Error checking email availability",
        available: false
      };
    }
  };

  // Debounced email validation function
  const validateEmail = useCallback((email) => {
    // Clear existing timer
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    // First check format
    const formatValidation = validateEmailFormat(email);
    
    if (!email) {
      setEmailValidation({ isValid: null, message: "", isChecking: false });
      return;
    }

    if (!formatValidation.isValid) {
      setEmailValidation({
        isValid: false,
        message: formatValidation.message,
        isChecking: false
      });
      return;
    }

    // Set checking state
    setEmailValidation({
      isValid: null,
      message: "Checking email availability...",
      isChecking: true
    });

    // Set new timer for uniqueness check
    const newTimer = setTimeout(async () => {
      const uniquenessResult = await checkEmailUniqueness(email);
      setEmailValidation({
        isValid: uniquenessResult.isValid,
        message: uniquenessResult.message,
        isChecking: false
      });
    }, 500); // 500ms debounce delay

    setDebounceTimer(newTimer);
  }, [debounceTimer]);

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [debounceTimer]);

  return {
    emailValidation,
    validateEmail,
    resetValidation: () => setEmailValidation({ isValid: null, message: "", isChecking: false })
  };
};

export default useEmailValidation;
