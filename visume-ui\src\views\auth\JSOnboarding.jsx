'use client'

import React, { useState, useRef, useEffect } from "react"
import { Check, ChevronDown, Upload } from "lucide-react"
import { Info, Star } from "lucide-react"
import Cookies from "js-cookie"

const cities = ["Bangalore", "Hyderabad", "Chennai", "Delhi"]
const skillSuggestions = ["JavaScript", "Python", "React", "Node.js", "CSS", "HTML", "Tailwind CSS", "Django"]
const languageSuggestions = ["English", "Hindi", "Kannada", "Spanish", "Tamil", "Telugu", "Malayalam", "Marathi"]

export default function Component() {
  const jsregcookie = (data) => {
    Cookies.set("candId", data.cand_id, { expires: 7 })
    Cookies.set("jstoken", data.token, { expires: 7 })
    Cookies.set("role", data.role, { expires: 7 })
  }

  const [step, setStep] = useState(1)
  const [onboardData, setOnboardData] = useState({
    gender: "",
    marks10th: "",
    marks12th: "",
    marksDegree: "",
    preferredLocation: "",
    skills: [],
    languages: [],
    profilePicture: null,
    resume: null,
  })
  const [errors, setErrors] = useState({})

  const [skillInput, setSkillInput] = useState("")
  const [langInput, setLangInput] = useState("")
  const [selectedSkills, setSelectedSkills] = useState([])
  const [selectedLang, setSelectedLang] = useState([])
  const [filteredSkillSuggestions, setFilteredSkillSuggestions] = useState([])
  const [filteredLangSuggestions, setFilteredLangSuggestions] = useState([])
  const [showSkillSuggestions, setShowSkillSuggestions] = useState(false)
  const [showLangSuggestions, setShowLangSuggestions] = useState(false)
  const skillsuggestionsRef = useRef(null)
  const langsuggestionsRef = useRef(null)

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (skillsuggestionsRef.current && !skillsuggestionsRef.current.contains(event.target)) {
        setShowSkillSuggestions(false)
      }
      if (langsuggestionsRef.current && !langsuggestionsRef.current.contains(event.target)) {
        setShowLangSuggestions(false)
      }
    }
    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  const handleInputChange = (e) => {
    const { name, value, type, files } = e.target

    if (type === "file") {
      setOnboardData((prev) => ({
        ...prev,
        [name]: files[0],
      }))
    } else if (name === "skills") {
      setSkillInput(value)
      updateSkillSuggestions(value)
    } else if (name === "languages") {
      setLangInput(value)
      updateLangSuggestions(value)
    } else {
      setOnboardData((prev) => ({ ...prev, [name]: value }))
    }
    setErrors((prev) => ({ ...prev, [name]: "" }))
  }

  const updateSkillSuggestions = (inputValue) => {
    const filtered = skillSuggestions.filter(
      (skill) => skill.toLowerCase().includes(inputValue.toLowerCase()) && !selectedSkills.includes(skill)
    )
    setFilteredSkillSuggestions(filtered)
    setShowSkillSuggestions(true)
  }

  const updateLangSuggestions = (inputValue) => {
    const filtered = languageSuggestions.filter(
      (lang) => lang.toLowerCase().includes(inputValue.toLowerCase()) && !selectedLang.includes(lang)
    )
    setFilteredLangSuggestions(filtered)
    setShowLangSuggestions(true)
  }

  const addSkill = (skill) => {
    if (!selectedSkills.includes(skill) && skillSuggestions.includes(skill)) {
      setSelectedSkills((prev) => [...prev, skill])
      setOnboardData((prev) => ({
        ...prev,
        skills: [...prev.skills, skill],
      }))
      setSkillInput("")
      setShowSkillSuggestions(false)
    }
  }

  const addLang = (lang) => {
    if (!selectedLang.includes(lang) && languageSuggestions.includes(lang)) {
      setSelectedLang((prev) => [...prev, lang])
      setOnboardData((prev) => ({
        ...prev,
        languages: [...prev.languages, lang],
      }))
      setLangInput("")
      setShowLangSuggestions(false)
    }
  }

  const removeSkill = (skill) => {
    setSelectedSkills((prev) => prev.filter((s) => s !== skill))
    setOnboardData((prev) => ({
      ...prev,
      skills: prev.skills.filter((s) => s !== skill),
    }))
  }

  const removeLang = (lang) => {
    setSelectedLang((prev) => prev.filter((l) => l !== lang))
    setOnboardData((prev) => ({
      ...prev,
      languages: prev.languages.filter((l) => l !== lang),
    }))
  }

  const handleKeyPressSkill = (e) => {
    if (e.key === "Enter") {
      e.preventDefault()
      const inputValue = skillInput.trim()
      if (skillSuggestions.includes(inputValue) && !selectedSkills.includes(inputValue)) {
        addSkill(inputValue)
      }
    }
  }

  const handleKeyPressLang = (e) => {
    if (e.key === "Enter") {
      e.preventDefault()
      const inputValue = langInput.trim()
      if (languageSuggestions.includes(inputValue) && !selectedLang.includes(inputValue)) {
        addLang(inputValue)
      }
    }
  }

  const handleFileChange = (e) => {
    const { name, files } = e.target
    setOnboardData((prev) => ({ ...prev, [name]: files[0] }))
    setErrors((prev) => ({ ...prev, [name]: "" }))
  }

  const validateStep = (currentStep) => {
    const newErrors = {}

    if (currentStep === 1) {
      if (!onboardData.gender) newErrors.gender = "Gender is required"
      if (!onboardData.marks10th) newErrors.marks10th = "10th Grade Marks are required"
      if (!onboardData.marks12th) newErrors.marks12th = "12th Grade Marks are required"
      if (!onboardData.marksDegree) newErrors.marksDegree = "Degree Marks are required"
      if (!onboardData.preferredLocation) newErrors.preferredLocation = "Preferred Location is required"
    } else if (currentStep === 2) {
      if (selectedSkills.length === 0) newErrors.skills = "Please select at least one skill"
      if (selectedLang.length === 0) newErrors.languages = "Please select at least one language"
    } else if (currentStep === 3) {
      if (!onboardData.profilePicture) newErrors.profilePicture = "Profile Picture is required"
      if (!onboardData.resume) newErrors.resume = "Resume is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validateStep(step)) {
      setStep((prev) => prev + 1)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (validateStep(3)) {
      const signupDataCookie = Cookies.get("formData")
      let signupData = {}
      if (signupDataCookie) {
        signupData = JSON.parse(signupDataCookie)
      }

      const finalFormData = {
        email: signupData.email,
        password: signupData.password,
        cand_name: signupData.firstName,
        cand_mobile: signupData.phoneNumber,
        marks: {
          "10th": onboardData.marks10th,
          "12th": onboardData.marks12th,
          degree: onboardData.marksDegree,
        },
        gender: onboardData.gender,
        languages_known: selectedLang,
        cand_skills: selectedSkills,
        preferred_location: onboardData.preferredLocation,
        resume: onboardData.resume,
        profile_picture: onboardData.profilePicture,
      }

      await registerJobseeker(finalFormData)
    }
  }

  async function registerJobseeker(formData) {
    const formPayload = new FormData()
    
    // Append all the fields
    formPayload.append("email", formData.email)
    formPayload.append("password", formData.password)
    formPayload.append("cand_name", formData.cand_name)
    formPayload.append("cand_mobile", formData.cand_mobile)
    formPayload.append("gender", formData.gender)
    formPayload.append("languages_known", JSON.stringify(formData.languages_known))
    formPayload.append("cand_skills", JSON.stringify(formData.cand_skills))
    formPayload.append("preferred_location", formData.preferred_location)
    formPayload.append("marks", JSON.stringify(formData.marks))

    if (formData.resume) {
      formPayload.append("resume", formData.resume)
    }
    if (formData.profile_picture) {
      formPayload.append("profile_picture", formData.profile_picture)
    }

    try {
      const response = await fetch('https://api.zoomjobs.in/api/v1/register-jobseeker', {
        method: 'POST',
        body: formPayload,
        credentials: 'include',
        headers: {
          'Accept': 'application/json'
        }
      })

      // Try to parse response
      let responseData
      try {
        responseData = await response.json()
      } catch (e) {
        throw new Error('Could not parse server response')
      }

      // Handle success
      if (response.ok) {
        console.log('Registration successful:', responseData.message)
        jsregcookie(responseData)
        window.location.href = "/candidate/dashboard"
        return
      }

      // Handle various error cases
      switch (response.status) {
        case 400:
          throw new Error(responseData.message || 'Please check all required fields')
        case 409:
          throw new Error(responseData.message || 'Email already exists')
        case 502:
          throw new Error('Server temporarily unavailable. Please try again later.')
        default:
          throw new Error(responseData.message || 'Registration failed')
      }
    } catch (error) {
      console.error('Registration error:', error)
      alert(error.message || 'Registration failed. Please try again later.')
    }
  }

  const steps = [
    { name: "Information", icon: Info },
    { name: "Skills", icon: Star },
    { name: "Upload Files", icon: Upload },
  ]

  return (
    <div className="mx-auto h-screen w-screen overflow-hidden rounded-lg bg-white shadow-lg">
      <div className="mb-4 flex items-center justify-between border-b bg-gray-50 h-16 p-2">
        <h1 className="text-3xl font-bold text-brand-600 hover:text-brand-800 transition duration-300 ease-in-out">
          Visume.ai
        </h1>
        <div className="relative">
          <div className="relative flex space-x-4 justify-between">
            {steps.map((s, i) => (
              <div key={s.name} className="flex flex-col items-center">
                <button
                  className={`w-8 h-8 rounded-full border-2 flex items-center justify-center transition-all duration-200 ease-in-out ${
                    step > i + 1
                      ? 'bg-primary border-primary text-primary-foreground'
                      : step === i + 1
                      ? 'bg-primary border-brand-300 text-brand-500'
                      : 'border-primary text-gray-400'
                  }`}
                  onClick={() => setStep(i + 1)}
                >
                  {step > i + 1 ? (
                    <Check className="w-6 h-6 text-brand-500" />
                  ) : (
                    <s.icon className="w-5 h-5" />
                  )}
                </button>
                <div className="mt-0 text-sm font-medium text-gray-700">{s.name}</div>
              </div>
            ))}
          </div>
        </div>
        <div className="flex h-10 space-x-2">
          <button
            className="rounded bg-gray-200 px-4 py-2 text-gray-700 hover:bg-gray-300"
            onClick={() => setStep((prev) => Math.max(prev - 1, 1))}
            disabled={step === 1}
          >
            Back
          </button>
          <button
            className="rounded bg-brand-500 px-4 py-2 text-white hover:bg-brand-600"
            onClick={step < 3 ? handleNext : handleSubmit}
          >
            {step === 3 ? "Submit" : "Next"}
          </button>
        </div>
      </div>
      <div className="w-screen h-screen">
        <form onSubmit={handleSubmit} encType="multipart/form-data" className="space-y-6 max-w-2xl mx-auto p-6">
          {step === 1 && (
            <>
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">Gender</label>
                <div className="flex space-x-4">
                  <label className="inline-flex items-center">
                    <input
                      type="radio"
                      className="form-radio text-blue-600"
                      name="gender"
                      value="male"
                      checked={onboardData.gender === "male"}
                      onChange={handleInputChange}
                    />
                    <span className="ml-2">Male</span>
                  </label>
                  <label className="inline-flex items-center">
                    <input
                      type="radio"
                      className="form-radio text-blue-600"
                      name="gender"
                      value="female"
                      checked={onboardData.gender === "female"}
                      onChange={handleInputChange}
                    />
                    <span className="ml-2">Female</span>
                  </label>
                </div>
                {errors.gender && <p className="mt-1 text-xs text-red-500">{errors.gender}</p>}
              </div>
              <div>
                <label htmlFor="marks10th" className="mb-1 block text-sm font-medium text-gray-700">
                  10th Grade Marks
                </label>
                <input
                  type="number"
                  id="marks10th"
                  name="marks10th"
                  min="1"
                  max="100"
                  value={onboardData.marks10th}
                  onChange={handleInputChange}
                  className="mt-2 block w-full rounded-md border border-gray-300 px-4 py-2 text-sm outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                />
                {errors.marks10th && <p className="mt-1 text-xs text-red-500">{errors.marks10th}</p>}
              </div>
              <div>
                <label htmlFor="marks12th" className="mb-1 block text-sm font-medium text-gray-700">
                  12th Grade Marks
                </label>
                <input
                  type="number"
                  id="marks12th"
                  name="marks12th"
                  min="1"
                  max="100"
                  value={onboardData.marks12th}
                  onChange={handleInputChange}
                  className="mt-2 block w-full rounded-md border border-gray-300 px-4 py-2 text-sm outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                />
                {errors.marks12th && <p className="mt-1 text-xs text-red-500">{errors.marks12th}</p>}
              </div>
              <div>
                <label htmlFor="marksDegree" className="mb-1 block text-sm font-medium text-gray-700">
                  Degree Marks
                </label>
                <input
                  type="number"
                  id="marksDegree"
                  name="marksDegree"
                  min="1"
                  max="100"
                  value={onboardData.marksDegree}
                  onChange={handleInputChange}
                  className="mt-2 block w-full rounded-md border border-gray-300 px-4 py-2 text-sm outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                />
                {errors.marksDegree && <p className="mt-1 text-xs text-red-500">{errors.marksDegree}</p>}
              </div>
              <div className="relative">
                <label htmlFor="preferredLocation" className="mb-1 block text-sm font-medium text-gray-700">
                  Preferred Location
                </label>
                <select
                  name="preferredLocation"
                  onChange={handleInputChange}
                  id="city-select"
                  className="form-input form-select singleSelect w-full mt-2 block rounded-md border border-gray-300 px-4 py-2 text-sm outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                >
                  <option value="" disabled selected>
                    Select your city
                  </option>
                  {cities.map((city) => (
                    <option key={city} value={city}>
                      {city}
                    </option>
                  ))}
                </select>
                {errors.preferredLocation && <p className="mt-1 text-xs text-red-500">{errors.preferredLocation}</p>}
              </div>
            </>
          )}

          {step === 2 && (
            <>
              <div className="relative">
                <label className="mb-1 block text-sm font-medium text-gray-700">Skills</label>
                <input
                  type="text"
                  name="skills"
                  value={skillInput}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyPressSkill}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  placeholder="Enter a skill and press enter"
                />
                <span className={`text-red-400 ${selectedSkills.length ? "hidden" : "block"}`}>
                  Select at least 1 skill
                </span>
                <ul
                  ref={skillsuggestionsRef}
                  className={`absolute z-10 max-h-[150px] overflow-auto rounded-md border border-gray-300 bg-white shadow-md ${
                    showSkillSuggestions ? "block" : "hidden"
                  }`}
                >
                  {filteredSkillSuggestions.length > 0 ? (
                    filteredSkillSuggestions.map((skill, index) => (
                      <li
                        key={index}
                        className="cursor-pointer px-3 py-2 hover:bg-gray-200"
                        onClick={() => addSkill(skill)}
                      >
                        {skill}
                      </li>
                    ))
                  ) : (
                    <li className="px-4 py-2 text-gray-500">No results found</li>
                  )}
                </ul>
              </div>
              <div className="mb-4 mt-3 h-auto max-h-[100px] w-full overflow-y-auto">
                <div className="flex flex-wrap gap-2">
                  {selectedSkills.map((skill, index) => (
                    <div
                      key={index}
                      className="flex items-center space-x-2 rounded-full bg-gray-200 px-3 py-1 text-gray-700"
                    >
                      <span>{skill}</span>
                      <button
                        type="button"
                        className="text-red-500 hover:text-red-700"
                        onClick={() => removeSkill(skill)}
                      >
                        &times;
                      </button>
                    </div>
                  ))}
                </div>
              </div>
              {errors.skills && <p className="mt-1 text-xs text-red-500">{errors.skills}</p>}
              <div className="relative">
                <label className="mb-1 block text-sm font-medium text-gray-700">Languages</label>
                <input
                  type="text"
                  name="languages"
                  id="lang-input"
                  value={langInput}
                  onChange={handleInputChange}
                  onKeyPress={handleKeyPressLang}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  placeholder="Enter a language and press enter"
                />
                <span id="langerr" className={`text-red-400 ${selectedLang.length === 0 ? "" : "hidden"}`}>
                  Select at least 1 language
                </span>
                {showLangSuggestions && (
                  <ul
                    ref={langsuggestionsRef}
                    id="langsuggestionsul"
                    className="absolute z-10 max-h-[150px] overflow-auto rounded-md border border-gray-300 bg-white shadow-md"
                  >
                    {filteredLangSuggestions.length > 0 ? (
                      filteredLangSuggestions.map((lang, index) => (
                        <li
                          key={index}
                          className="cursor-pointer px-3 py-2 hover:bg-gray-200"
                          onClick={() => addLang(lang)}
                        >
                          {lang}
                        </li>
                      ))
                    ) : (
                      <li className="px-4 py-2 text-gray-500">No results found</li>
                    )}
                  </ul>
                )}
              </div>
              <div className="mt-3 h-auto max-h-[100px] w-full overflow-y-auto">
                <div id="lang-container" className="flex flex-wrap gap-2">
                  {selectedLang.map((lang, index) => (
                    <div
                      key={index}
                      className="flex items-center space-x-2 rounded-full bg-gray-200 px-3 py-1 text-gray-700"
                    >
                      <span>{lang}</span>
                      <button className="text-red-500 hover:text-red-700" onClick={() => removeLang(lang)}>
                        &times;
                      </button>
                    </div>
                  ))}
                </div>
              </div>
              {errors.languages && <p className="mt-1 text-xs text-red-500">{errors.languages}</p>}
            </>
          )}

          {step === 3 && (
            <>
              <div className="space-y-4">
                <div>
                  <label htmlFor="profilePicture" className="mb-1 block text-sm font-medium text-gray-700">
                    Profile Picture
                  </label>
                  <div className="mt-1 flex items-center justify-around rounded-md border-2 border-dashed border-gray-300 px-6 pb-6 pt-5">
                    <div className="space-y-1 text-center">
                      <Upload className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="flex text-sm text-gray-600">
                        <label
                          htmlFor="profilePicture"
                          className="relative cursor-pointer rounded-md bg-white font-medium text-blue-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2 hover:text-blue-500"
                        >
                          <span>Upload a file</span>
                          <input
                            id="profilePicture"
                            name="profilePicture"
                            type="file"
                            className="sr-only"
                            accept=".jpeg,.jpg,.png"
                            onChange={handleFileChange}
                          />
                        </label>
                        <p className="pl-1">or drag and drop</p>
                      </div>
                      <p className="text-xs text-gray-500">PNG, JPG, JPEG up to 10MB</p>
                    </div>
                    {onboardData.profilePicture && (
                      <img
                        src={URL.createObjectURL(onboardData.profilePicture)}
                        alt="Profile Preview"
                        className="mt-2 h-32 w-32 rounded-full object-cover"
                      />
                    )}
                  </div>
                  {errors.profilePicture && <p className="mt-1 text-xs text-red-500">{errors.profilePicture}</p>}
                </div>
                <div>
                  <label htmlFor="resume" className="mb-1 block text-sm font-medium text-gray-700">
                    Resume
                  </label>
                  <div className="mt-1 flex justify-center rounded-md border-2 border-dashed border-gray-300 px-6 pb-6 pt-5">
                    <div className="space-y-1 text-center">
                      <Upload className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="flex text-sm text-gray-600">
                        <label
                          htmlFor="resume"
                          className="relative cursor-pointer rounded-md bg-white font-medium text-blue-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2 hover:text-blue-500"
                        >
                          <span>Upload a file</span>
                          <input
                            id="resume"
                            name="resume"
                            type="file"
                            className="sr-only"
                            accept=".pdf"
                            onChange={handleFileChange}
                          />
                        </label>
                        <p className="pl-1">or drag and drop</p>
                      </div>
                      <p className="text-xs text-gray-500">PDF up to 10MB</p>
                    </div>
                  </div>
                  {onboardData.resume && (
                    <p className="mt-2 text-sm text-gray-600">File uploaded: {onboardData.resume.name}</p>
                  )}
                  {errors.resume && <p className="mt-1 text-xs text-red-500">{errors.resume}</p>}
                </div>
              </div>
            </>
          )}
        </form>
      </div>
    </div>
  )
}